export type PluginName = 'angular' | 'astro' | 'ava' | 'babel' | 'biome' | 'bun' | 'c8' | 'capacitor' | 'changelogen' | 'changelogithub' | 'changesets' | 'commitizen' | 'commitlint' | 'convex' | 'create-typescript-app' | 'cspell' | 'cucumber' | 'cypress' | 'dependency-cruiser' | 'docusaurus' | 'dotenv' | 'drizzle' | 'eleventy' | 'eslint' | 'expo' | 'gatsby' | 'github-action' | 'github-actions' | 'glob' | 'graphql-codegen' | 'hardhat' | 'husky' | 'i18next-parser' | 'jest' | 'karma' | 'ladle' | 'lefthook' | 'lint-staged' | 'linthtml' | 'lockfile-lint' | 'lost-pixel' | 'markdownlint' | 'metro' | 'mocha' | 'moonrepo' | 'msw' | 'nano-staged' | 'nest' | 'netlify' | 'next' | 'node' | 'nodemon' | 'npm-package-json-lint' | 'nuxt' | 'nx' | 'nyc' | 'oclif' | 'oxlint' | 'playwright' | 'playwright-ct' | 'playwright-test' | 'plop' | 'postcss' | 'preconstruct' | 'prettier' | 'prisma' | 'react-cosmos' | 'react-router' | 'relay' | 'release-it' | 'remark' | 'remix' | 'rollup' | 'rsbuild' | 'rspack' | 'semantic-release' | 'sentry' | 'simple-git-hooks' | 'size-limit' | 'sst' | 'starlight' | 'storybook' | 'stryker' | 'stylelint' | 'svelte' | 'svgo' | 'syncpack' | 'tailwind' | 'travis' | 'ts-node' | 'tsdown' | 'tsup' | 'tsx' | 'typedoc' | 'typescript' | 'unbuild' | 'unocss' | 'vercel-og' | 'vike' | 'vite' | 'vitest' | 'vue' | 'webdriver-io' | 'webpack' | 'wireit' | 'wrangler' | 'xo' | 'yarn' | 'yorkie';
export declare const pluginNames: readonly ["angular", "astro", "ava", "babel", "biome", "bun", "c8", "capacitor", "changelogen", "changelogithub", "changesets", "commitizen", "commitlint", "convex", "create-typescript-app", "cspell", "cucumber", "cypress", "dependency-cruiser", "docusaurus", "dotenv", "drizzle", "eleventy", "eslint", "expo", "gatsby", "github-action", "github-actions", "glob", "graphql-codegen", "hardhat", "husky", "i18next-parser", "jest", "karma", "ladle", "lefthook", "lint-staged", "linthtml", "lockfile-lint", "lost-pixel", "markdownlint", "metro", "mocha", "moonrepo", "msw", "nano-staged", "nest", "netlify", "next", "node", "nodemon", "npm-package-json-lint", "nuxt", "nx", "nyc", "oclif", "oxlint", "playwright", "playwright-ct", "playwright-test", "plop", "postcss", "preconstruct", "prettier", "prisma", "react-cosmos", "react-router", "relay", "release-it", "remark", "remix", "rollup", "rsbuild", "rspack", "semantic-release", "sentry", "simple-git-hooks", "size-limit", "sst", "starlight", "storybook", "stryker", "stylelint", "svelte", "svgo", "syncpack", "tailwind", "travis", "ts-node", "tsdown", "tsup", "tsx", "typedoc", "typescript", "unbuild", "unocss", "vercel-og", "vike", "vite", "vitest", "vue", "webdriver-io", "webpack", "wireit", "wrangler", "xo", "yarn", "yorkie"];
