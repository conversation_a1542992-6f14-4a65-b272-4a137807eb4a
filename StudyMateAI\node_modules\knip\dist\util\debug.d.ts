export declare const inspect: (obj: unknown) => void;
export declare const debugLog: (context: string, message: string) => void;
export declare const debugLogObject: (context: string | [string, string], name: string, obj: unknown | (() => unknown)) => void;
export declare const debugLogArray: (context: string | [string, string], message: string, elements: string[] | Set<string> | (() => string[] | Set<string>)) => void;
