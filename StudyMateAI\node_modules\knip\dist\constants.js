export const ROOT_WORKSPACE_NAME = '.';
export const IMPORT_STAR = '*';
export const ANONYMOUS = '__anonymous';
export const KNIP_CONFIG_LOCATIONS = [
    'knip.json',
    'knip.jsonc',
    '.knip.json',
    '.knip.jsonc',
    'knip.ts',
    'knip.js',
    'knip.config.ts',
    'knip.config.js',
];
export const DEFAULT_EXTENSIONS = ['.js', '.mjs', '.cjs', '.jsx', '.ts', '.tsx', '.mts', '.cts'];
export const GLOBAL_IGNORE_PATTERNS = ['**/node_modules/**', '.yarn'];
export const PUBLIC_TAG = '@public';
export const INTERNAL_TAG = '@internal';
export const BETA_TAG = '@beta';
export const ALIAS_TAG = '@alias';
export const DT_SCOPE = '@types';
export const PROTOCOL_VIRTUAL = 'virtual:';
export const IGNORED_GLOBAL_BINARIES = new Set([
    'amplify',
    'aws',
    'base64',
    'basename',
    'bash',
    'bun',
    'bundle',
    'bunx',
    'cargo',
    'cat',
    'cd',
    'chmod',
    'chown',
    'cksum',
    'cmd',
    'comm',
    'command',
    'corepack',
    'cp',
    'curl',
    'cut',
    'deno',
    'df',
    'dir',
    'dirname',
    'docker',
    'echo',
    'env',
    'exec',
    'exit',
    'expand',
    'export',
    'expr',
    'factor',
    'false',
    'find',
    'gem',
    'git',
    'grep',
    'groups',
    'gzip',
    'head',
    'id',
    'join',
    'kill',
    'ln',
    'logname',
    'ls',
    'md5sum',
    'mkdir',
    'mknod',
    'mv',
    'nice',
    'nl',
    'node',
    'nohup',
    'npm',
    'nproc',
    'npx',
    'paste',
    'pnpm',
    'pnpx',
    'pr',
    'printenv',
    'pwd',
    'rm',
    'rmdir',
    'rsync',
    'scp',
    'seq',
    'set',
    'sh',
    'sha1sum',
    'sha512sum',
    'shred',
    'shuf',
    'sort',
    'split',
    'ssh',
    'stat',
    'stty',
    'sudo',
    'sync',
    'tac',
    'tee',
    'test',
    'timeout',
    'touch',
    'tr',
    'true',
    'tsort',
    'tty',
    'uname',
    'unexpand',
    'uniq',
    'wc',
    'who',
    'whoami',
    'xargs',
    'xcodebuild',
    'xvfb-run',
    'yarn',
    'yes',
    'zip',
]);
export const IGNORED_DEPENDENCIES = new Set(['knip', 'typescript']);
export const IGNORED_RUNTIME_DEPENDENCIES = new Set(['node', 'bun', 'deno']);
export const FOREIGN_FILE_EXTENSIONS = new Set([
    '.avif',
    '.css',
    '.eot',
    '.gif',
    '.html',
    '.ico',
    '.jpeg',
    '.jpg',
    '.less',
    '.mp3',
    '.png',
    '.sass',
    '.scss',
    '.sh',
    '.svg',
    '.ttf',
    '.webp',
    '.woff',
    '.woff2',
    '.yaml',
    '.yml',
]);
export const IGNORE_DEFINITELY_TYPED = new Set([
    'node',
    'bun',
    'jest',
]);
export const ISSUE_TYPES = [
    'files',
    'dependencies',
    'devDependencies',
    'optionalPeerDependencies',
    'unlisted',
    'binaries',
    'unresolved',
    'exports',
    'nsExports',
    'types',
    'nsTypes',
    'enumMembers',
    'classMembers',
    'duplicates',
];
export const ISSUE_TYPE_TITLE = {
    files: 'Unused files',
    _files: 'Unused files',
    dependencies: 'Unused dependencies',
    devDependencies: 'Unused devDependencies',
    optionalPeerDependencies: 'Referenced optional peerDependencies',
    unlisted: 'Unlisted dependencies',
    binaries: 'Unlisted binaries',
    unresolved: 'Unresolved imports',
    exports: 'Unused exports',
    nsExports: 'Exports in used namespace',
    types: 'Unused exported types',
    nsTypes: 'Exported types in used namespace',
    enumMembers: 'Unused exported enum members',
    classMembers: 'Unused exported class members',
    duplicates: 'Duplicate exports',
};
export const FIX_FLAGS = {
    NONE: 0,
    OBJECT_BINDING: 1 << 0,
    EMPTY_DECLARATION: 1 << 1,
    WITH_NEWLINE: 1 << 2,
};
