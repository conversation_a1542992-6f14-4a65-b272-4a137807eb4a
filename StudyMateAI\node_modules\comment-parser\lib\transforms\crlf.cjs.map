{"version": 3, "sources": ["crlf.js"], "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "defineProperty", "exports", "value", "util_js_1", "require", "order", "crlf", "ending", "update", "line", "assign", "tokens", "lineEnd", "_a", "source", "fields", "rewireSource", "map", "default"], "mappings": "AAAA;;AACA,IAAIA,MAAM,GAAI,QAAQ,KAAKA,MAAd,IAAyB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;AAClD,MAAIC,CAAC,GAAG,EAAR;;AACA,OAAK,IAAIC,CAAT,IAAcH,CAAd,EAAiB,IAAII,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCP,CAArC,EAAwCG,CAAxC,KAA8CF,CAAC,CAACO,OAAF,CAAUL,CAAV,IAAe,CAAjE,EACbD,CAAC,CAACC,CAAD,CAAD,GAAOH,CAAC,CAACG,CAAD,CAAR;;AACJ,MAAIH,CAAC,IAAI,IAAL,IAAa,OAAOI,MAAM,CAACK,qBAAd,KAAwC,UAAzD,EACI,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWP,CAAC,GAAGC,MAAM,CAACK,qBAAP,CAA6BT,CAA7B,CAApB,EAAqDU,CAAC,GAAGP,CAAC,CAACQ,MAA3D,EAAmED,CAAC,EAApE,EAAwE;AACpE,QAAIT,CAAC,CAACO,OAAF,CAAUL,CAAC,CAACO,CAAD,CAAX,IAAkB,CAAlB,IAAuBN,MAAM,CAACC,SAAP,CAAiBO,oBAAjB,CAAsCL,IAAtC,CAA2CP,CAA3C,EAA8CG,CAAC,CAACO,CAAD,CAA/C,CAA3B,EACIR,CAAC,CAACC,CAAC,CAACO,CAAD,CAAF,CAAD,GAAUV,CAAC,CAACG,CAAC,CAACO,CAAD,CAAF,CAAX;AACP;AACL,SAAOR,CAAP;AACH,CAVD;;AAWAE,MAAM,CAACS,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,EAAAA,KAAK,EAAE;AAAT,CAA7C;;AACA,MAAMC,SAAS,GAAGC,OAAH,eAAf;;AACA,MAAMC,KAAK,GAAG,CACV,KADU,EAEV,aAFU,EAGV,UAHU,EAIV,MAJU,EAKV,UALU,EAMV,MANU,EAOV,SAPU,EAQV,KARU,EASV,eATU,EAUV,WAVU,EAWV,OAXU,CAAd;;AAaA,SAASC,IAAT,CAAcC,MAAd,EAAsB;AAClB,WAASC,MAAT,CAAgBC,IAAhB,EAAsB;AAClB,WAAOlB,MAAM,CAACmB,MAAP,CAAcnB,MAAM,CAACmB,MAAP,CAAc,EAAd,EAAkBD,IAAlB,CAAd,EAAuC;AAAEE,MAAAA,MAAM,EAAEpB,MAAM,CAACmB,MAAP,CAAcnB,MAAM,CAACmB,MAAP,CAAc,EAAd,EAAkBD,IAAI,CAACE,MAAvB,CAAd,EAA8C;AAAEC,QAAAA,OAAO,EAAEL,MAAM,KAAK,IAAX,GAAkB,EAAlB,GAAuB;AAAlC,OAA9C;AAAV,KAAvC,CAAP;AACH;;AACD,SAAQM,EAAD,IAAQ;AACX,QAAI;AAAEC,MAAAA;AAAF,QAAaD,EAAjB;AAAA,QAAqBE,MAAM,GAAG7B,MAAM,CAAC2B,EAAD,EAAK,CAAC,QAAD,CAAL,CAApC;;AACA,WAAO,CAAC,GAAGV,SAAS,CAACa,YAAd,EAA4BzB,MAAM,CAACmB,MAAP,CAAcnB,MAAM,CAACmB,MAAP,CAAc,EAAd,EAAkBK,MAAlB,CAAd,EAAyC;AAAED,MAAAA,MAAM,EAAEA,MAAM,CAACG,GAAP,CAAWT,MAAX;AAAV,KAAzC,CAA5B,CAAP;AACH,GAHD;AAIH;;AACDP,OAAO,CAACiB,OAAR,GAAkBZ,IAAlB", "sourcesContent": ["\"use strict\";\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_js_1 = require(\"../util.js\");\nconst order = [\n    'end',\n    'description',\n    'postType',\n    'type',\n    'postName',\n    'name',\n    'postTag',\n    'tag',\n    'postDelimiter',\n    'delimiter',\n    'start',\n];\nfunction crlf(ending) {\n    function update(line) {\n        return Object.assign(Object.assign({}, line), { tokens: Object.assign(Object.assign({}, line.tokens), { lineEnd: ending === 'LF' ? '' : '\\r' }) });\n    }\n    return (_a) => {\n        var { source } = _a, fields = __rest(_a, [\"source\"]);\n        return (0, util_js_1.rewireSource)(Object.assign(Object.assign({}, fields), { source: source.map(update) }));\n    };\n}\nexports.default = crlf;\n"], "file": "crlf.cjs"}