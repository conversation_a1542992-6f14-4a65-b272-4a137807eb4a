import type { AnyRuleModule } from '@typescript-eslint/utils/ts-eslint';
import type { TesterConfigWithDefaults } from '../types';
type GetAdditionalRule = (ruleId: string) => AnyRuleModule | null;
/**
 * Validates an entire config object.
 * @param config The config object to validate.
 * @param source The name of the configuration source to report in any errors.
 * @param getAdditionalRule A map from strings to loaded rules.
 */
export declare function validate(config: TesterConfigWithDefaults, source: string, getAdditionalRule: GetAdditionalRule): void;
export {};
//# sourceMappingURL=config-validator.d.ts.map