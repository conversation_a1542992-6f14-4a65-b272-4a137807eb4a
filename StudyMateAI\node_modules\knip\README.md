<h1 align="center">
  <br />
  <a href="https://knip.dev">
    <img height="200" width="200" src="https://knip.dev/favicon.svg" alt="Knip" />
  </a>
  <br />
  <br />
</h1>

<div align="center">

[![NPM Version][2]][1] [![NPM Downloads][3]][1] [![GitHub Repo stars][5]][4]

</div>

Knip finds and fixes **unused dependencies, exports and files** in your
JavaScript and TypeScript projects. Less code and dependencies lead to improved
performance, less maintenance and easier refactorings.

- Website: [knip.dev][6]
- GitHub repo: [webpro-nl/knip][4]
- npm package: [knip][1]
- [Discord][7]
- [Contributing Guide][8]
- [Sponsor Knip!][9]

## Contributors

Special thanks to [the wonderful people who have contributed to <PERSON>ni<PERSON>][10]!

## Knip

/'knɪp/ means "(to) cut" and is [pronounced with a hard "K"][11] 🇳🇱

## License

Knip is free and open-source software licensed under the [ISC License][12].

Parts of Knip have been inspired by and/or partially copy code from the
following projects:

- [@npmcli/package-json][17] ([ISC][18])
- [@pnpm/deps.graph-sequencer][15] ([MIT][16])
- [file-entry-cache][13] ([MIT][14])
- [json-parse-even-better-errors][19] ([MIT][20])

[1]: https://www.npmjs.com/package/knip
[2]: https://img.shields.io/npm/v/knip?color=f56e0f
[3]: https://img.shields.io/npm/dm/knip?color=f56e0f
[4]: https://github.com/webpro-nl/knip
[5]:
  https://img.shields.io/github/stars/webpro-nl/knip?style=flat-square&color=f56e0f
[6]: https://knip.dev
[7]: https://discord.gg/r5uXTtbTpc
[8]: https://github.com/webpro-nl/knip/blob/main/.github/CONTRIBUTING.md
[9]: https://knip.dev/sponsors
[10]: https://knip.dev/#created-by-awesome-contributors
[11]: https://www.youtube.com/watch?v=PE7h7KvQoUI&t=9s
[12]: ./license
[13]: https://github.com/jaredwray/cacheable/tree/main/packages/file-entry-cache
[14]:
  https://github.com/jaredwray/cacheable/blob/main/packages/file-entry-cache/LICENSE
[15]: https://github.com/pnpm/pnpm/tree/main/deps/graph-sequencer
[16]: https://github.com/pnpm/pnpm/blob/main/LICENSE
[17]: https://github.com/npm/package-json
[18]: https://github.com/npm/package-json/blob/main/LICENSE
[19]: https://github.com/npm/json-parse-even-better-errors
[20]: https://github.com/npm/json-parse-even-better-errors/blob/main/LICENSE.md
