/*
 * ---------------------------------------------------------
 * Copyright(C) Microsoft Corporation. All rights reserved.
 * ---------------------------------------------------------
 *
 * ---------------------------------------------------------
 * Generated file, DO NOT EDIT
 * ---------------------------------------------------------
 */
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeInfo = exports.RunState = exports.RunResult = exports.RepositoryType = exports.GetLogExpandOptions = exports.GetArtifactExpandOptions = exports.ConfigurationType = void 0;
const VSSInterfaces = require("../interfaces/common/VSSInterfaces");
var ConfigurationType;
(function (ConfigurationType) {
    /**
     * Unknown type.
     */
    ConfigurationType[ConfigurationType["Unknown"] = 0] = "Unknown";
    /**
     * YAML.
     */
    ConfigurationType[ConfigurationType["Yaml"] = 1] = "Yaml";
    /**
     * Designer JSON.
     */
    ConfigurationType[ConfigurationType["DesignerJson"] = 2] = "DesignerJson";
    /**
     * Just-in-time.
     */
    ConfigurationType[ConfigurationType["JustInTime"] = 3] = "JustInTime";
    /**
     * Designer-JSON.
     */
    ConfigurationType[ConfigurationType["DesignerHyphenJson"] = 2] = "DesignerHyphenJson";
})(ConfigurationType = exports.ConfigurationType || (exports.ConfigurationType = {}));
/**
 * Expansion options for GetArtifact and ListArtifacts.
 */
var GetArtifactExpandOptions;
(function (GetArtifactExpandOptions) {
    /**
     * No expansion.
     */
    GetArtifactExpandOptions[GetArtifactExpandOptions["None"] = 0] = "None";
    /**
     * Include signed content.
     */
    GetArtifactExpandOptions[GetArtifactExpandOptions["SignedContent"] = 1] = "SignedContent";
})(GetArtifactExpandOptions = exports.GetArtifactExpandOptions || (exports.GetArtifactExpandOptions = {}));
/**
 * $expand options for GetLog and ListLogs.
 */
var GetLogExpandOptions;
(function (GetLogExpandOptions) {
    GetLogExpandOptions[GetLogExpandOptions["None"] = 0] = "None";
    GetLogExpandOptions[GetLogExpandOptions["SignedContent"] = 1] = "SignedContent";
})(GetLogExpandOptions = exports.GetLogExpandOptions || (exports.GetLogExpandOptions = {}));
var RepositoryType;
(function (RepositoryType) {
    RepositoryType[RepositoryType["Unknown"] = 0] = "Unknown";
    RepositoryType[RepositoryType["GitHub"] = 1] = "GitHub";
    RepositoryType[RepositoryType["AzureReposGit"] = 2] = "AzureReposGit";
    RepositoryType[RepositoryType["GitHubEnterprise"] = 3] = "GitHubEnterprise";
    RepositoryType[RepositoryType["AzureReposGitHyphenated"] = 2] = "AzureReposGitHyphenated";
})(RepositoryType = exports.RepositoryType || (exports.RepositoryType = {}));
/**
 * This is not a Flags enum because we don't want to set multiple results on a build. However, when adding values, please stick to powers of 2 as if it were a Flags enum. This will make it easier to query multiple results.
 */
var RunResult;
(function (RunResult) {
    RunResult[RunResult["Unknown"] = 0] = "Unknown";
    RunResult[RunResult["Succeeded"] = 1] = "Succeeded";
    RunResult[RunResult["Failed"] = 2] = "Failed";
    RunResult[RunResult["Canceled"] = 4] = "Canceled";
})(RunResult = exports.RunResult || (exports.RunResult = {}));
/**
 * This is not a Flags enum because we don't want to set multiple states on a build. However, when adding values, please stick to powers of 2 as if it were a Flags enum. This will make it easier to query multiple states.
 */
var RunState;
(function (RunState) {
    RunState[RunState["Unknown"] = 0] = "Unknown";
    RunState[RunState["InProgress"] = 1] = "InProgress";
    RunState[RunState["Canceling"] = 2] = "Canceling";
    RunState[RunState["Completed"] = 4] = "Completed";
})(RunState = exports.RunState || (exports.RunState = {}));
exports.TypeInfo = {
    Artifact: {},
    ConfigurationType: {
        enumValues: {
            "unknown": 0,
            "yaml": 1,
            "designerJson": 2,
            "justInTime": 3,
            "designerHyphenJson": 2
        }
    },
    CreatePipelineConfigurationParameters: {},
    CreatePipelineParameters: {},
    GetArtifactExpandOptions: {
        enumValues: {
            "none": 0,
            "signedContent": 1
        }
    },
    GetLogExpandOptions: {
        enumValues: {
            "none": 0,
            "signedContent": 1
        }
    },
    Log: {},
    LogCollection: {},
    Pipeline: {},
    PipelineConfiguration: {},
    Repository: {},
    RepositoryResource: {},
    RepositoryType: {
        enumValues: {
            "unknown": 0,
            "gitHub": 1,
            "azureReposGit": 2,
            "gitHubEnterprise": 3,
            "azureReposGitHyphenated": 2
        }
    },
    Run: {},
    RunResources: {},
    RunResult: {
        enumValues: {
            "unknown": 0,
            "succeeded": 1,
            "failed": 2,
            "canceled": 4
        }
    },
    RunState: {
        enumValues: {
            "unknown": 0,
            "inProgress": 1,
            "canceling": 2,
            "completed": 4
        }
    },
    SignalRConnection: {},
};
exports.TypeInfo.Artifact.fields = {
    signedContent: {
        typeInfo: VSSInterfaces.TypeInfo.SignedUrl
    }
};
exports.TypeInfo.CreatePipelineConfigurationParameters.fields = {
    type: {
        enumType: exports.TypeInfo.ConfigurationType
    }
};
exports.TypeInfo.CreatePipelineParameters.fields = {
    configuration: {
        typeInfo: exports.TypeInfo.CreatePipelineConfigurationParameters
    }
};
exports.TypeInfo.Log.fields = {
    createdOn: {
        isDate: true,
    },
    lastChangedOn: {
        isDate: true,
    },
    signedContent: {
        typeInfo: VSSInterfaces.TypeInfo.SignedUrl
    }
};
exports.TypeInfo.LogCollection.fields = {
    logs: {
        isArray: true,
        typeInfo: exports.TypeInfo.Log
    },
    signedContent: {
        typeInfo: VSSInterfaces.TypeInfo.SignedUrl
    }
};
exports.TypeInfo.Pipeline.fields = {
    configuration: {
        typeInfo: exports.TypeInfo.PipelineConfiguration
    }
};
exports.TypeInfo.PipelineConfiguration.fields = {
    type: {
        enumType: exports.TypeInfo.ConfigurationType
    }
};
exports.TypeInfo.Repository.fields = {
    type: {
        enumType: exports.TypeInfo.RepositoryType
    }
};
exports.TypeInfo.RepositoryResource.fields = {
    repository: {
        typeInfo: exports.TypeInfo.Repository
    }
};
exports.TypeInfo.Run.fields = {
    createdDate: {
        isDate: true,
    },
    finishedDate: {
        isDate: true,
    },
    resources: {
        typeInfo: exports.TypeInfo.RunResources
    },
    result: {
        enumType: exports.TypeInfo.RunResult
    },
    state: {
        enumType: exports.TypeInfo.RunState
    }
};
exports.TypeInfo.RunResources.fields = {
    repositories: {
        isDictionary: true,
        dictionaryValueTypeInfo: exports.TypeInfo.RepositoryResource
    }
};
exports.TypeInfo.SignalRConnection.fields = {
    signedContent: {
        typeInfo: VSSInterfaces.TypeInfo.SignedUrl
    }
};
