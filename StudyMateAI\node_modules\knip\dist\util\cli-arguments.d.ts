export declare const helpText = "\u2702\uFE0F  Find unused dependencies, exports and files in your JavaScript and TypeScript projects\n\nUsage: knip [options]\n\nOptions:\n  -c, --config [file]      Configuration file path (default: [.]knip.json[c], knip.(js|ts), knip.config.(js|ts) or package.json#knip)\n  -t, --tsConfig [file]    TypeScript configuration path (default: tsconfig.json)\n  --production             Analyze only production source files (e.g. no test files, devDependencies)\n  --strict                 Consider only direct dependencies of workspace (not devDependencies, not other workspaces)\n  -W, --workspace [dir]    Analyze a single workspace (default: analyze all configured workspaces)\n  --directory [dir]        Run process from a different directory (default: cwd)\n  --cache                  Enable caching\n  --cache-location         Change cache location (default: node_modules/.cache/knip)\n  --watch                  Watch mode\n  --no-gitignore           Don't use .gitignore\n  --include                Report only provided issue type(s), can be comma-separated or repeated (1)\n  --exclude                Exclude provided issue type(s) from report, can be comma-separated or repeated (1)\n  --dependencies           Shortcut for --include dependencies,unlisted,binaries,unresolved\n  --exports                Shortcut for --include exports,nsExports,classMembers,types,nsTypes,enumMembers,duplicates\n  --files                  Shortcut for --include files\n  --fix                    Fix issues\n  --fix-type               Fix only issues of type, can be comma-separated or repeated (2)\n  --format                 Format modified files after --fix using the local formatter\n  --allow-remove-files     Allow Knip to remove files (with --fix)\n  --include-libs           Include type definitions from external dependencies (default: false)\n  --include-entry-exports  Include entry files when reporting unused exports\n  --isolate-workspaces     Isolate workspaces into separate programs\n  -n, --no-progress        Don't show dynamic progress updates (automatically enabled in CI environments)\n  --preprocessor           Preprocess the results before providing it to the reporter(s), can be repeated\n  --preprocessor-options   Pass extra options to the preprocessor (as JSON string, see --reporter-options example)\n  --reporter               Select reporter: symbols, compact, codeowners, json, codeclimate, markdown, disclosure, can be repeated (default: symbols)\n  --reporter-options       Pass extra options to the reporter (as JSON string, see example)\n  --tags                   Include or exclude tagged exports\n  --no-config-hints        Suppress configuration hints\n  --treat-config-hints-as-errors    Exit with non-zero code (1) if there are any configuration hints\n  --no-exit-code           Always exit with code zero (0)\n  --max-issues             Maximum number of issues before non-zero exit code (default: 0)\n  -d, --debug              Show debug output\n  --trace                  Show trace output\n  --trace-export [name]    Show trace output for named export(s)\n  --trace-file [file]      Show trace output for exports in file\n  --performance            Measure count and running time of key functions and display stats table\n  --performance-fn [name]  Measure only function [name]\n  --memory                 Measure memory usage and display data table\n  --memory-realtime        Log memory usage in realtime\n  -h, --help               Print this help text\n  -V, --version            Print version\n\n(1) Issue types: files, dependencies, unlisted, unresolved, exports, nsExports, classMembers, types, nsTypes, enumMembers, duplicates\n(2) Fixable issue types: dependencies, exports, types\n\nExamples:\n\n$ knip\n$ knip --production\n$ knip --workspace packages/client --include files,dependencies\n$ knip -c ./config/knip.json --reporter compact\n$ knip --reporter codeowners --reporter-options '{\"path\":\".github/CODEOWNERS\"}'\n$ knip --tags=-lintignore\n\nWebsite: https://knip.dev";
declare const _default: {
    cache?: boolean | undefined;
    'cache-location'?: string | undefined;
    config?: string | undefined;
    debug?: boolean | undefined;
    dependencies?: boolean | undefined;
    directory?: string | undefined;
    exclude?: string[] | undefined;
    exports?: boolean | undefined;
    tags?: string[] | undefined;
    'experimental-tags'?: string[] | undefined;
    files?: boolean | undefined;
    fix?: boolean | undefined;
    'fix-type'?: string[] | undefined;
    format?: boolean | undefined;
    'allow-remove-files'?: boolean | undefined;
    help?: boolean | undefined;
    'ignore-internal'?: boolean | undefined;
    include?: string[] | undefined;
    'include-libs'?: boolean | undefined;
    'include-entry-exports'?: boolean | undefined;
    'isolate-workspaces'?: boolean | undefined;
    'max-issues'?: string | undefined;
    memory?: boolean | undefined;
    'memory-realtime'?: boolean | undefined;
    'no-config-hints'?: boolean | undefined;
    'no-exit-code'?: boolean | undefined;
    'no-gitignore'?: boolean | undefined;
    'no-progress'?: boolean | undefined;
    performance?: boolean | undefined;
    'performance-fn'?: string | undefined;
    production?: boolean | undefined;
    preprocessor?: string[] | undefined;
    'preprocessor-options'?: string | undefined;
    reporter?: string[] | undefined;
    'reporter-options'?: string | undefined;
    strict?: boolean | undefined;
    trace?: boolean | undefined;
    'trace-export'?: string | undefined;
    'trace-file'?: string | undefined;
    'treat-config-hints-as-errors'?: boolean | undefined;
    tsConfig?: string | undefined;
    version?: boolean | undefined;
    watch?: boolean | undefined;
    workspace?: string | undefined;
};
export default _default;
