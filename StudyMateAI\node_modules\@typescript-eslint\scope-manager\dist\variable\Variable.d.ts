import { VariableBase } from './VariableBase';
/**
 * A Variable represents a locally scoped identifier. These include arguments to functions.
 */
export declare class Variable extends VariableBase {
    /**
     * `true` if the variable is valid in a type context, false otherwise
     * @public
     */
    get isTypeVariable(): boolean;
    /**
     * `true` if the variable is valid in a value context, false otherwise
     * @public
     */
    get isValueVariable(): boolean;
}
//# sourceMappingURL=Variable.d.ts.map