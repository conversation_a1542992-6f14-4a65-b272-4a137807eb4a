{"version": 3, "file": "InvalidTestCase.d.ts", "sourceRoot": "", "sources": ["../../src/types/InvalidTestCase.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAChF,OAAO,KAAK,EAAE,2BAA2B,EAAE,MAAM,oCAAoC,CAAC;AAEtF,OAAO,KAAK,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AACnE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAErD,MAAM,WAAW,gBAAgB,CAAC,UAAU,SAAS,MAAM;IACzD;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,2BAA2B,CAAC;IAC5C;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,UAAU,CAAC;IAC/B;;;OAGG;IACH,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;CAIzB;AAED,MAAM,WAAW,aAAa,CAAC,UAAU,SAAS,MAAM;IACtD;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,2BAA2B,CAAC;IAC5C;;OAEG;IACH,QAAQ,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC;IAC5B;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;IAC1B;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,UAAU,CAAC;IAC/B;;OAEG;IACH,QAAQ,CAAC,WAAW,CAAC,EAAE,SAAS,gBAAgB,CAAC,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC;IACtE;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,cAAc,GAAG,eAAe,CAAC;CAIlD;AAED,MAAM,WAAW,eAAe,CAC9B,UAAU,SAAS,MAAM,EACzB,OAAO,SAAS,SAAS,OAAO,EAAE,CAClC,SAAQ,aAAa,CAAC,OAAO,CAAC;IAC9B;;OAEG;IACH,QAAQ,CAAC,qBAAqB,CAAC,EAAE,oBAAoB,CAAC;IACtD;;OAEG;IACH,QAAQ,CAAC,MAAM,EAAE,SAAS,aAAa,CAAC,UAAU,CAAC,EAAE,CAAC;IACtD;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,IAAI,CAAC;CAC5C"}