import picocolors from 'picocolors';
import { ISSUE_TYPE_TITLE } from '../../constants.js';
import { SymbolType } from '../../types/issues.js';
import { relative } from '../../util/path.js';
import { Table } from '../../util/table.js';
export const plain = (text) => text;
export const dim = picocolors.gray;
export const bright = picocolors.whiteBright;
export const yellow = picocolors.yellow;
export const getIssueTypeTitle = (reportType) => ISSUE_TYPE_TITLE[reportType];
export const getColoredTitle = (title, count) => `${picocolors.yellowBright(picocolors.underline(title))} (${count})`;
export const getDimmedTitle = (title, count) => `${yellow(`${picocolors.underline(title)} (${count})`)}`;
export const getIssueLine = ({ owner, filePath, symbols, parentSymbol, severity }) => {
    const symbol = symbols ? `: ${symbols.map(s => s.symbol).join(', ')}` : '';
    const parent = parentSymbol ? ` (${parentSymbol})` : '';
    const print = severity === 'warn' ? dim : plain;
    return `${owner ? `${picocolors.cyan(owner)} ` : ''}${print(`${relative(filePath)}${symbol}${parent}`)}`;
};
export const convert = (issue) => ({
    name: issue.symbol,
    line: issue.line,
    col: issue.col,
    pos: issue.pos,
});
const sortByPos = (a, b) => {
    const [filePathA, rowA, colA] = a.filePath.split(':');
    const [filePathB, rowB, colB] = b.filePath.split(':');
    return filePathA === filePathB
        ? Number(rowA) === Number(rowB)
            ? Number(colA) - Number(colB)
            : Number(rowA) - Number(rowB)
        : filePathA.localeCompare(filePathB);
};
const highlightSymbol = (issue) => (_) => {
    if (issue.specifier && issue.specifier !== issue.symbol && issue.specifier.includes(issue.symbol)) {
        const parts = issue.specifier.split(issue.symbol);
        const rest = parts.slice(1).join('');
        return [dim(parts[0]), bright(issue.symbol), dim(rest)].join('');
    }
    return issue.symbol;
};
export const getTableForType = (issues, options = { isUseColors: true }) => {
    const table = new Table({ truncateStart: ['filePath'], noTruncate: ['symbolType'] });
    for (const issue of issues.sort(sortByPos)) {
        table.row();
        const print = options.isUseColors && (issue.isFixed || issue.severity === 'warn') ? dim : plain;
        const symbol = issue.symbols ? issue.symbols.map(s => s.symbol).join(', ') : issue.symbol;
        table.cell('symbol', print(symbol), options.isUseColors ? highlightSymbol(issue) : () => symbol);
        table.cell('parentSymbol', issue.parentSymbol && print(issue.parentSymbol));
        table.cell('symbolType', issue.symbolType && issue.symbolType !== SymbolType.UNKNOWN && print(issue.symbolType));
        const pos = issue.line === undefined ? '' : `:${issue.line}${issue.col === undefined ? '' : `:${issue.col}`}`;
        const cell = issue.type === 'files' ? '' : `${relative(issue.filePath)}${pos}`;
        table.cell('filePath', print(cell));
        table.cell('fixed', issue.isFixed && print('(removed)'));
    }
    return table;
};
