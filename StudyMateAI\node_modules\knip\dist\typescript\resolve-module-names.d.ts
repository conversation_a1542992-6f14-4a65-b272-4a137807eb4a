import ts from 'typescript';
import type { ToSourceFilePath } from '../util/to-source-path.js';
export type ResolveModuleNames = ReturnType<typeof createCustomModuleResolver>;
export declare function createCustomModuleResolver(compilerOptions: ts.CompilerOptions, customCompilerExtensions: string[], toSourceFilePath: ToSourceFilePath, useCache?: boolean, isSkipLibs?: boolean): (moduleNames: string[], containingFile: string) => Array<ts.ResolvedModuleFull | undefined>;
