import type { ConfigurationChief, Workspace } from '../ConfigurationChief.js';
import type { ConsoleStreamer } from '../ConsoleStreamer.js';
import type { DependencyDeputy } from '../DependencyDeputy.js';
import type { IssueCollector } from '../IssueCollector.js';
import type { PrincipalFactory } from '../PrincipalFactory.js';
import type { ProjectPrincipal } from '../ProjectPrincipal.js';
import type { Tags } from '../types/cli.js';
import type { Report } from '../types/issues.js';
import type { ModuleGraph } from '../types/module-graph.js';
interface BuildOptions {
    cacheLocation: string;
    chief: ConfigurationChief;
    collector: IssueCollector;
    cwd: string;
    deputy: DependencyDeputy;
    factory: PrincipalFactory;
    gitignore: boolean;
    isCache: boolean;
    isFixExports: boolean;
    isFixTypes: boolean;
    isGitIgnored: (path: string) => boolean;
    isIsolateWorkspaces: boolean;
    isProduction: boolean;
    isSkipLibs: boolean;
    isStrict: boolean;
    isWatch: boolean;
    report: Report;
    streamer: ConsoleStreamer;
    tags: Tags;
    tsConfigFile?: string;
    workspaces: Workspace[];
}
export declare function build({ cacheLocation, chief, collector, cwd, deputy, factory, gitignore, isCache, isFixExports, isFixTypes, isGitIgnored, isIsolateWorkspaces, isProduction, isSkipLibs, isStrict, isWatch, report, streamer, tags, tsConfigFile, workspaces, }: BuildOptions): Promise<{
    graph: ModuleGraph;
    entryPaths: Set<string>;
    analyzedFiles: Set<string>;
    unreferencedFiles: Set<string>;
    analyzeSourceFile: (filePath: string, principal: ProjectPrincipal) => void;
}>;
export {};
