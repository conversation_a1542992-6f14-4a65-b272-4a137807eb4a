export declare const isAbsolute: (path: string) => boolean;
export declare const dirname: (path: string) => string;
export declare const extname: (path: string) => string;
export declare const basename: (path: string, suffix?: string) => string;
export declare const join: (...paths: string[]) => string;
export declare const toPosix: (value: string) => string;
export declare const cwd: string;
export declare const resolve: (...paths: string[]) => string;
export declare const relative: (from: string, to?: string) => string;
export declare const isInNodeModules: (filePath: string) => boolean;
export declare const toAbsolute: (id: string, base?: string) => string;
export declare const toRelative: (id: string) => string;
export declare const isInternal: (id: string) => boolean;
