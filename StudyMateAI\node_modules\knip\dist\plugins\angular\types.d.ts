type FileVersion = number;
export type Project = Project1 & {
    cli?: {
        [k: string]: unknown;
    };
    schematics?: SchematicOptions;
    prefix?: string;
    root: string;
    i18n?: I18N;
    sourceRoot?: string;
    projectType: 'application' | 'library';
    architect?: {
        [k: string]: {
            builder: string;
            defaultConfiguration?: string;
            options?: {
                [k: string]: unknown;
            };
            configurations?: {
                [k: string]: {
                    [k: string]: unknown;
                };
            };
        } | {
            builder?: '@angular/build:application';
            defaultConfiguration?: string;
            options?: ApplicationSchemaForBuildFacade;
            configurations?: {
                [k: string]: ApplicationSchemaForBuildFacade;
            };
        } | {
            builder?: '@angular-devkit/build-angular:application';
            defaultConfiguration?: string;
            options?: ApplicationSchemaForBuildFacade;
            configurations?: {
                [k: string]: ApplicationSchemaForBuildFacade;
            };
        } | {
            builder?: '@angular-devkit/build-angular:app-shell';
            defaultConfiguration?: string;
            options?: AppShellTarget;
            configurations?: {
                [k: string]: AppShellTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:browser';
            defaultConfiguration?: string;
            options?: WebpackBrowserSchemaForBuildFacade;
            configurations?: {
                [k: string]: WebpackBrowserSchemaForBuildFacade;
            };
        } | {
            builder?: '@angular-devkit/build-angular:browser-esbuild';
            defaultConfiguration?: string;
            options?: EsbuildBrowserSchemaForBuildFacade;
            configurations?: {
                [k: string]: EsbuildBrowserSchemaForBuildFacade;
            };
        } | {
            builder?: '@angular/build:dev-server';
            defaultConfiguration?: string;
            options?: DevServerTarget;
            configurations?: {
                [k: string]: DevServerTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:dev-server';
            defaultConfiguration?: string;
            options?: DevServerTarget1;
            configurations?: {
                [k: string]: DevServerTarget1;
            };
        } | {
            builder?: '@angular/build:extract-i18n';
            defaultConfiguration?: string;
            options?: ExtractI18NTarget;
            configurations?: {
                [k: string]: ExtractI18NTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:extract-i18n';
            defaultConfiguration?: string;
            options?: ExtractI18NTarget1;
            configurations?: {
                [k: string]: ExtractI18NTarget1;
            };
        } | {
            builder?: '@angular-devkit/build-angular:karma';
            defaultConfiguration?: string;
            options?: KarmaTarget;
            configurations?: {
                [k: string]: KarmaTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:jest';
            defaultConfiguration?: string;
            options?: JestBrowserSchemaForBuildFacade;
            configurations?: {
                [k: string]: JestBrowserSchemaForBuildFacade;
            };
        } | {
            builder?: '@angular-devkit/build-angular:web-test-runner';
            defaultConfiguration?: string;
            options?: WebTestRunnerTarget;
            configurations?: {
                [k: string]: WebTestRunnerTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:prerender';
            defaultConfiguration?: string;
            options?: PrerenderTarget;
            configurations?: {
                [k: string]: PrerenderTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:ssr-dev-server';
            defaultConfiguration?: string;
            options?: SSRDevServerTarget;
            configurations?: {
                [k: string]: SSRDevServerTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:server';
            defaultConfiguration?: string;
            options?: UniversalTarget;
            configurations?: {
                [k: string]: UniversalTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:ng-packagr';
            defaultConfiguration?: string;
            options?: NgPackagrTarget;
            configurations?: {
                [k: string]: NgPackagrTarget;
            };
        };
    };
    targets?: {
        [k: string]: {
            builder: string;
            defaultConfiguration?: string;
            options?: {
                [k: string]: unknown;
            };
            configurations?: {
                [k: string]: {
                    [k: string]: unknown;
                };
            };
        } | {
            builder?: '@angular/build:application';
            defaultConfiguration?: string;
            options?: ApplicationSchemaForBuildFacade;
            configurations?: {
                [k: string]: ApplicationSchemaForBuildFacade;
            };
        } | {
            builder?: '@angular-devkit/build-angular:application';
            defaultConfiguration?: string;
            options?: ApplicationSchemaForBuildFacade;
            configurations?: {
                [k: string]: ApplicationSchemaForBuildFacade;
            };
        } | {
            builder?: '@angular-devkit/build-angular:app-shell';
            defaultConfiguration?: string;
            options?: AppShellTarget;
            configurations?: {
                [k: string]: AppShellTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:browser';
            defaultConfiguration?: string;
            options?: WebpackBrowserSchemaForBuildFacade;
            configurations?: {
                [k: string]: WebpackBrowserSchemaForBuildFacade;
            };
        } | {
            builder?: '@angular-devkit/build-angular:browser-esbuild';
            defaultConfiguration?: string;
            options?: EsbuildBrowserSchemaForBuildFacade;
            configurations?: {
                [k: string]: EsbuildBrowserSchemaForBuildFacade;
            };
        } | {
            builder?: '@angular/build:dev-server';
            defaultConfiguration?: string;
            options?: DevServerTarget;
            configurations?: {
                [k: string]: DevServerTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:dev-server';
            defaultConfiguration?: string;
            options?: DevServerTarget1;
            configurations?: {
                [k: string]: DevServerTarget1;
            };
        } | {
            builder?: '@angular/build:extract-i18n';
            defaultConfiguration?: string;
            options?: ExtractI18NTarget;
            configurations?: {
                [k: string]: ExtractI18NTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:extract-i18n';
            defaultConfiguration?: string;
            options?: ExtractI18NTarget1;
            configurations?: {
                [k: string]: ExtractI18NTarget1;
            };
        } | {
            builder?: '@angular-devkit/build-angular:karma';
            defaultConfiguration?: string;
            options?: KarmaTarget;
            configurations?: {
                [k: string]: KarmaTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:jest';
            defaultConfiguration?: string;
            options?: JestBrowserSchemaForBuildFacade;
            configurations?: {
                [k: string]: JestBrowserSchemaForBuildFacade;
            };
        } | {
            builder?: '@angular-devkit/build-angular:web-test-runner';
            defaultConfiguration?: string;
            options?: WebTestRunnerTarget;
            configurations?: {
                [k: string]: WebTestRunnerTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:prerender';
            defaultConfiguration?: string;
            options?: PrerenderTarget;
            configurations?: {
                [k: string]: PrerenderTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:ssr-dev-server';
            defaultConfiguration?: string;
            options?: SSRDevServerTarget;
            configurations?: {
                [k: string]: SSRDevServerTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:server';
            defaultConfiguration?: string;
            options?: UniversalTarget;
            configurations?: {
                [k: string]: UniversalTarget;
            };
        } | {
            builder?: '@angular-devkit/build-angular:ng-packagr';
            defaultConfiguration?: string;
            options?: NgPackagrTarget;
            configurations?: {
                [k: string]: NgPackagrTarget;
            };
        };
    };
    [k: string]: unknown;
};
type Project1 = {
    [k: string]: unknown;
};
type PrerenderTarget = {
    [k: string]: unknown;
};
export interface AngularCLIWorkspaceConfiguration {
    $schema?: string;
    version: FileVersion;
    cli?: CliOptions;
    schematics?: SchematicOptions;
    newProjectRoot?: string;
    projects?: {
        [k: string]: Project;
    };
}
interface CliOptions {
    schematicCollections?: string[];
    packageManager?: 'npm' | 'cnpm' | 'yarn' | 'pnpm' | 'bun';
    warnings?: {
        versionMismatch?: boolean;
    };
    analytics?: boolean | string;
    cache?: {
        environment?: 'local' | 'ci' | 'all';
        enabled?: boolean;
        path?: string;
    };
}
interface SchematicOptions {
    '@schematics/angular:application'?: AngularApplicationOptionsSchema;
    '@schematics/angular:class'?: AngularClassOptionsSchema;
    '@schematics/angular:component'?: AngularComponentOptionsSchema;
    '@schematics/angular:directive'?: AngularDirectiveOptionsSchema;
    '@schematics/angular:enum'?: AngularEnumOptionsSchema;
    '@schematics/angular:guard'?: AngularGuardOptionsSchema;
    '@schematics/angular:interceptor'?: AngularInterceptorOptionsSchema;
    '@schematics/angular:interface'?: AngularInterfaceOptionsSchema;
    '@schematics/angular:library'?: LibraryOptionsSchema;
    '@schematics/angular:pipe'?: AngularPipeOptionsSchema;
    '@schematics/angular:ng-new'?: AngularNgNewOptionsSchema;
    '@schematics/angular:resolver'?: AngularResolverOptionsSchema;
    '@schematics/angular:service'?: AngularServiceOptionsSchema;
    '@schematics/angular:web-worker'?: AngularWebWorkerOptionsSchema;
    [k: string]: unknown;
}
interface AngularApplicationOptionsSchema {
    projectRoot?: string;
    name: string;
    inlineStyle?: boolean;
    inlineTemplate?: boolean;
    viewEncapsulation?: 'Emulated' | 'None' | 'ShadowDom';
    routing?: boolean;
    prefix?: string;
    style?: 'css' | 'scss' | 'sass' | 'less';
    skipTests?: boolean;
    skipPackageJson?: boolean;
    minimal?: boolean;
    skipInstall?: boolean;
    strict?: boolean;
    standalone?: boolean;
    ssr?: boolean;
    serverRouting?: boolean;
    experimentalZoneless?: boolean;
}
interface AngularClassOptionsSchema {
    name: string;
    path?: string;
    project: string;
    skipTests?: boolean;
    type?: string;
}
interface AngularComponentOptionsSchema {
    path?: string;
    project: string;
    name: string;
    displayBlock?: boolean;
    inlineStyle?: boolean;
    inlineTemplate?: boolean;
    standalone?: boolean;
    viewEncapsulation?: 'Emulated' | 'None' | 'ShadowDom';
    changeDetection?: 'Default' | 'OnPush';
    prefix?: {
        [k: string]: unknown;
    } & string;
    style?: 'css' | 'scss' | 'sass' | 'less' | 'none';
    type?: string;
    skipTests?: boolean;
    flat?: boolean;
    skipImport?: boolean;
    selector?: string;
    skipSelector?: boolean;
    module?: string;
    export?: boolean;
    exportDefault?: boolean;
}
interface AngularDirectiveOptionsSchema {
    name: string;
    path?: string;
    project: string;
    prefix?: {
        [k: string]: unknown;
    } & string;
    skipTests?: boolean;
    skipImport?: boolean;
    selector?: string;
    standalone?: boolean;
    flat?: boolean;
    module?: string;
    export?: boolean;
}
interface AngularEnumOptionsSchema {
    name: string;
    path?: string;
    project: string;
    type?: string;
}
interface AngularGuardOptionsSchema {
    name: string;
    skipTests?: boolean;
    flat?: boolean;
    path?: string;
    project: string;
    functional?: boolean;
    implements?: [
        'CanActivate' | 'CanActivateChild' | 'CanDeactivate' | 'CanMatch',
        ...('CanActivate' | 'CanActivateChild' | 'CanDeactivate' | 'CanMatch')[]
    ];
}
interface AngularInterceptorOptionsSchema {
    name: string;
    path?: string;
    project: string;
    flat?: boolean;
    skipTests?: boolean;
    functional?: boolean;
}
interface AngularInterfaceOptionsSchema {
    name: string;
    path?: string;
    project: string;
    prefix?: string;
    type?: string;
}
interface LibraryOptionsSchema {
    name: string;
    entryFile?: string;
    prefix?: string;
    skipPackageJson?: boolean;
    skipInstall?: boolean;
    skipTsConfig?: boolean;
    projectRoot?: string;
    standalone?: boolean;
}
interface AngularPipeOptionsSchema {
    name: string;
    path?: string;
    project: string;
    flat?: boolean;
    skipTests?: boolean;
    skipImport?: boolean;
    standalone?: boolean;
    module?: string;
    export?: boolean;
}
interface AngularNgNewOptionsSchema {
    directory?: string;
    name: string;
    skipInstall?: boolean;
    skipGit?: boolean;
    commit?: boolean | {
        name: string;
        email: string;
        message?: string;
        [k: string]: unknown;
    };
    newProjectRoot?: string;
    inlineStyle?: boolean;
    inlineTemplate?: boolean;
    viewEncapsulation?: 'Emulated' | 'None' | 'ShadowDom';
    version: string;
    routing?: boolean;
    prefix?: string;
    style?: 'css' | 'scss' | 'sass' | 'less';
    skipTests?: boolean;
    createApplication?: boolean;
    minimal?: boolean;
    strict?: boolean;
    packageManager?: 'npm' | 'yarn' | 'pnpm' | 'cnpm' | 'bun';
    standalone?: boolean;
    ssr?: boolean;
    serverRouting?: boolean;
    experimentalZoneless?: boolean;
}
interface AngularResolverOptionsSchema {
    name: string;
    skipTests?: boolean;
    flat?: boolean;
    functional?: boolean;
    path?: string;
    project: string;
}
interface AngularServiceOptionsSchema {
    name: string;
    path?: string;
    project: string;
    flat?: boolean;
    skipTests?: boolean;
}
interface AngularWebWorkerOptionsSchema {
    path?: string;
    project: string;
    name: string;
    snippet?: boolean;
}
interface I18N {
    sourceLocale?: string | {
        code?: string;
        baseHref?: string;
    };
    locales?: {
        [k: string]: string | string[] | {
            translation?: string | string[];
            baseHref?: string;
        };
    };
}
interface ApplicationSchemaForBuildFacade {
    assets?: ({
        followSymlinks?: boolean;
        glob: string;
        input: string;
        ignore?: string[];
        output?: string;
    } | string)[];
    browser: string;
    server?: (string | false) & string;
    polyfills?: string[];
    tsConfig: string;
    deployUrl?: string;
    security?: {
        autoCsp?: {
            unsafeEval?: boolean;
        } | boolean;
    };
    scripts?: ({
        input: string;
        bundleName?: string;
        inject?: boolean;
    } | string)[];
    styles?: ({
        input: string;
        bundleName?: string;
        inject?: boolean;
    } | string)[];
    inlineStyleLanguage?: 'css' | 'less' | 'sass' | 'scss';
    stylePreprocessorOptions?: {
        includePaths?: string[];
        sass?: {
            fatalDeprecations?: string[];
            silenceDeprecations?: string[];
            futureDeprecations?: string[];
        };
    };
    externalDependencies?: string[];
    clearScreen?: boolean;
    optimization?: {
        scripts?: boolean;
        styles?: {
            minify?: boolean;
            inlineCritical?: boolean;
            removeSpecialComments?: boolean;
        } | boolean;
        fonts?: {
            inline?: boolean;
        } | boolean;
    } | boolean;
    loader?: {
        [k: string]: 'text' | 'binary' | 'file' | 'empty';
    };
    define?: {
        [k: string]: string;
    };
    fileReplacements?: FileReplacement[];
    outputPath: {
        base: string;
        browser?: string;
        server?: string;
        media?: string;
    } | string;
    aot?: boolean;
    sourceMap?: {
        scripts?: boolean;
        styles?: boolean;
        hidden?: boolean;
        vendor?: boolean;
    } | boolean;
    baseHref?: string;
    verbose?: boolean;
    progress?: boolean;
    i18nMissingTranslation?: 'warning' | 'error' | 'ignore';
    i18nDuplicateTranslation?: 'warning' | 'error' | 'ignore';
    localize?: boolean | [string, ...string[]];
    watch?: boolean;
    outputHashing?: 'none' | 'all' | 'media' | 'bundles';
    poll?: number;
    deleteOutputPath?: boolean;
    preserveSymlinks?: boolean;
    extractLicenses?: boolean;
    namedChunks?: boolean;
    subresourceIntegrity?: boolean;
    serviceWorker?: string | false;
    index: string | {
        input: string;
        output?: string;
        preloadInitial?: boolean;
        [k: string]: unknown;
    } | false;
    statsJson?: boolean;
    budgets?: Budget[];
    webWorkerTsConfig?: string;
    crossOrigin?: 'none' | 'anonymous' | 'use-credentials';
    allowedCommonJsDependencies?: string[];
    prerender?: boolean | {
        routesFile?: string;
        discoverRoutes?: boolean;
    };
    ssr?: boolean | {
        entry?: string;
        experimentalPlatform?: 'node' | 'neutral';
    };
    appShell?: boolean;
    outputMode?: 'static' | 'server';
}
interface FileReplacement {
    replace: string;
    with: string;
}
interface Budget {
    type: 'all' | 'allScript' | 'any' | 'anyScript' | 'anyComponentStyle' | 'bundle' | 'initial';
    name?: string;
    baseline?: string;
    maximumWarning?: string;
    maximumError?: string;
    minimumWarning?: string;
    minimumError?: string;
    warning?: string;
    error?: string;
}
interface AppShellTarget {
    browserTarget: string;
    serverTarget: string;
    appModuleBundle?: string;
    route?: string;
    inputIndexPath?: string;
    outputIndexPath?: string;
}
export interface WebpackBrowserSchemaForBuildFacade {
    assets?: ({
        followSymlinks?: boolean;
        glob: string;
        input: string;
        ignore?: string[];
        output?: string;
    } | string)[];
    main: string;
    polyfills?: string[] | string;
    tsConfig: string;
    scripts?: ({
        input: string;
        bundleName?: string;
        inject?: boolean;
    } | string)[];
    styles?: ({
        input: string;
        bundleName?: string;
        inject?: boolean;
    } | string)[];
    inlineStyleLanguage?: 'css' | 'less' | 'sass' | 'scss';
    stylePreprocessorOptions?: {
        includePaths?: string[];
    };
    optimization?: {
        scripts?: boolean;
        styles?: {
            minify?: boolean;
            inlineCritical?: boolean;
        } | boolean;
        fonts?: {
            inline?: boolean;
        } | boolean;
    } | boolean;
    fileReplacements?: ({
        src: string;
        replaceWith: string;
    } | {
        replace: string;
        with: string;
    })[];
    outputPath: string;
    resourcesOutputPath?: string;
    aot?: boolean;
    sourceMap?: {
        scripts?: boolean;
        styles?: boolean;
        hidden?: boolean;
        vendor?: boolean;
    } | boolean;
    vendorChunk?: boolean;
    commonChunk?: boolean;
    baseHref?: string;
    deployUrl?: string;
    verbose?: boolean;
    progress?: boolean;
    i18nMissingTranslation?: 'warning' | 'error' | 'ignore';
    i18nDuplicateTranslation?: 'warning' | 'error' | 'ignore';
    localize?: boolean | [string, ...string[]];
    watch?: boolean;
    outputHashing?: 'none' | 'all' | 'media' | 'bundles';
    poll?: number;
    deleteOutputPath?: boolean;
    preserveSymlinks?: boolean;
    extractLicenses?: boolean;
    buildOptimizer?: boolean;
    namedChunks?: boolean;
    subresourceIntegrity?: boolean;
    serviceWorker?: boolean;
    ngswConfigPath?: string;
    index: string | {
        input: string;
        output?: string;
        [k: string]: unknown;
    };
    statsJson?: boolean;
    budgets?: Budget1[];
    webWorkerTsConfig?: string;
    crossOrigin?: 'none' | 'anonymous' | 'use-credentials';
    allowedCommonJsDependencies?: string[];
}
interface Budget1 {
    type: 'all' | 'allScript' | 'any' | 'anyScript' | 'anyComponentStyle' | 'bundle' | 'initial';
    name?: string;
    baseline?: string;
    maximumWarning?: string;
    maximumError?: string;
    minimumWarning?: string;
    minimumError?: string;
    warning?: string;
    error?: string;
}
interface EsbuildBrowserSchemaForBuildFacade {
    assets?: ({
        followSymlinks?: boolean;
        glob: string;
        input: string;
        ignore?: string[];
        output?: string;
    } | string)[];
    main: string;
    polyfills?: string[] | string;
    tsConfig: string;
    scripts?: ({
        input: string;
        bundleName?: string;
        inject?: boolean;
    } | string)[];
    styles?: ({
        input: string;
        bundleName?: string;
        inject?: boolean;
    } | string)[];
    inlineStyleLanguage?: 'css' | 'less' | 'sass' | 'scss';
    stylePreprocessorOptions?: {
        includePaths?: string[];
    };
    externalDependencies?: string[];
    optimization?: {
        scripts?: boolean;
        styles?: {
            minify?: boolean;
            inlineCritical?: boolean;
        } | boolean;
        fonts?: {
            inline?: boolean;
        } | boolean;
    } | boolean;
    fileReplacements?: FileReplacement1[];
    outputPath: string;
    resourcesOutputPath?: string;
    aot?: boolean;
    sourceMap?: {
        scripts?: boolean;
        styles?: boolean;
        hidden?: boolean;
        vendor?: boolean;
    } | boolean;
    vendorChunk?: boolean;
    commonChunk?: boolean;
    baseHref?: string;
    deployUrl?: string;
    verbose?: boolean;
    progress?: boolean;
    i18nMissingTranslation?: 'warning' | 'error' | 'ignore';
    i18nDuplicateTranslation?: 'warning' | 'error' | 'ignore';
    localize?: boolean | [string, ...string[]];
    watch?: boolean;
    outputHashing?: 'none' | 'all' | 'media' | 'bundles';
    poll?: number;
    deleteOutputPath?: boolean;
    preserveSymlinks?: boolean;
    extractLicenses?: boolean;
    buildOptimizer?: boolean;
    namedChunks?: boolean;
    subresourceIntegrity?: boolean;
    serviceWorker?: boolean;
    ngswConfigPath?: string;
    index: string | {
        input: string;
        output?: string;
        [k: string]: unknown;
    } | false;
    statsJson?: boolean;
    budgets?: Budget2[];
    webWorkerTsConfig?: string;
    crossOrigin?: 'none' | 'anonymous' | 'use-credentials';
    allowedCommonJsDependencies?: string[];
}
interface FileReplacement1 {
    replace: string;
    with: string;
}
interface Budget2 {
    type: 'all' | 'allScript' | 'any' | 'anyScript' | 'anyComponentStyle' | 'bundle' | 'initial';
    name?: string;
    baseline?: string;
    maximumWarning?: string;
    maximumError?: string;
    minimumWarning?: string;
    minimumError?: string;
    warning?: string;
    error?: string;
}
interface DevServerTarget {
    buildTarget: string;
    port?: number;
    host?: string;
    proxyConfig?: string;
    ssl?: boolean;
    sslKey?: string;
    sslCert?: string;
    headers?: {
        [k: string]: string;
    };
    open?: boolean;
    verbose?: boolean;
    liveReload?: boolean;
    servePath?: string;
    hmr?: boolean;
    watch?: boolean;
    poll?: number;
    inspect?: string | boolean;
    prebundle?: boolean | {
        exclude: string[];
    };
}
interface DevServerTarget1 {
    buildTarget: string;
    port?: number;
    host?: string;
    proxyConfig?: string;
    ssl?: boolean;
    sslKey?: string;
    sslCert?: string;
    headers?: {
        [k: string]: string;
    };
    open?: boolean;
    verbose?: boolean;
    liveReload?: boolean;
    publicHost?: string;
    allowedHosts?: string[];
    servePath?: string;
    disableHostCheck?: boolean;
    hmr?: boolean;
    watch?: boolean;
    poll?: number;
    inspect?: string | boolean;
    forceEsbuild?: boolean;
    prebundle?: boolean | {
        exclude: string[];
    };
}
interface ExtractI18NTarget {
    buildTarget?: string;
    format?: 'xmb' | 'xlf' | 'xlif' | 'xliff' | 'xlf2' | 'xliff2' | 'json' | 'arb' | 'legacy-migrate';
    progress?: boolean;
    outputPath?: string;
    outFile?: string;
}
interface ExtractI18NTarget1 {
    buildTarget?: string;
    format?: 'xmb' | 'xlf' | 'xlif' | 'xliff' | 'xlf2' | 'xliff2' | 'json' | 'arb' | 'legacy-migrate';
    progress?: boolean;
    outputPath?: string;
    outFile?: string;
}
export interface KarmaTarget {
    main?: string;
    tsConfig: string;
    karmaConfig?: string;
    polyfills?: string[] | string;
    assets?: ({
        glob: string;
        input: string;
        output?: string;
        ignore?: string[];
    } | string)[];
    scripts?: ({
        input: string;
        bundleName?: string;
        inject?: boolean;
    } | string)[];
    styles?: ({
        input: string;
        bundleName?: string;
        inject?: boolean;
    } | string)[];
    inlineStyleLanguage?: 'css' | 'less' | 'sass' | 'scss';
    stylePreprocessorOptions?: {
        includePaths?: string[];
    };
    include?: string[];
    exclude?: string[];
    sourceMap?: {
        scripts?: boolean;
        styles?: boolean;
        vendor?: boolean;
    } | boolean;
    progress?: boolean;
    watch?: boolean;
    poll?: number;
    preserveSymlinks?: boolean;
    browsers?: string | false;
    codeCoverage?: boolean;
    codeCoverageExclude?: string[];
    fileReplacements?: ({
        src: string;
        replaceWith: string;
    } | {
        replace: string;
        with: string;
    })[];
    reporters?: string[];
    builderMode?: 'detect' | 'browser' | 'application';
    webWorkerTsConfig?: string;
}
interface JestBrowserSchemaForBuildFacade {
    include?: string[];
    exclude?: string[];
    tsConfig: string;
    polyfills?: string[];
}
interface WebTestRunnerTarget {
    main?: string;
    tsConfig: string;
    polyfills?: string[] | string;
    assets?: ({
        glob: string;
        input: string;
        output?: string;
        ignore?: string[];
    } | string)[];
    scripts?: ({
        input: string;
        bundleName?: string;
        inject?: boolean;
    } | string)[];
    styles?: ({
        input: string;
        bundleName?: string;
        inject?: boolean;
    } | string)[];
    inlineStyleLanguage?: 'css' | 'less' | 'sass' | 'scss';
    stylePreprocessorOptions?: {
        includePaths?: string[];
    };
    include?: string[];
    exclude?: string[];
    sourceMap?: {
        scripts?: boolean;
        styles?: boolean;
        vendor?: boolean;
    } | boolean;
    progress?: boolean;
    watch?: boolean;
    poll?: number;
    preserveSymlinks?: boolean;
    browsers?: string;
    codeCoverage?: boolean;
    codeCoverageExclude?: string[];
    fileReplacements?: ({
        src: string;
        replaceWith: string;
    } | {
        replace: string;
        with: string;
    })[];
    webWorkerTsConfig?: string;
}
interface SSRDevServerTarget {
    browserTarget: string;
    serverTarget: string;
    host?: string;
    port?: number;
    watch?: boolean;
    publicHost?: string;
    open?: boolean;
    progress?: boolean;
    inspect?: boolean;
    ssl?: boolean;
    sslKey?: string;
    sslCert?: string;
    proxyConfig?: string;
    verbose?: boolean;
}
interface UniversalTarget {
    assets?: ({
        followSymlinks?: boolean;
        glob: string;
        input: string;
        ignore?: string[];
        output?: string;
    } | string)[];
    main: string;
    tsConfig: string;
    inlineStyleLanguage?: 'css' | 'less' | 'sass' | 'scss';
    stylePreprocessorOptions?: {
        includePaths?: string[];
    };
    optimization?: {
        scripts?: boolean;
        styles?: boolean;
    } | boolean;
    fileReplacements?: ({
        src: string;
        replaceWith: string;
    } | {
        replace: string;
        with: string;
    })[];
    outputPath: string;
    resourcesOutputPath?: string;
    sourceMap?: {
        scripts?: boolean;
        styles?: boolean;
        hidden?: boolean;
        vendor?: boolean;
    } | boolean;
    deployUrl?: string;
    vendorChunk?: boolean;
    verbose?: boolean;
    progress?: boolean;
    i18nMissingTranslation?: 'warning' | 'error' | 'ignore';
    i18nDuplicateTranslation?: 'warning' | 'error' | 'ignore';
    localize?: boolean | [string, ...string[]];
    outputHashing?: 'none' | 'all' | 'media' | 'bundles';
    deleteOutputPath?: boolean;
    preserveSymlinks?: boolean;
    extractLicenses?: boolean;
    buildOptimizer?: boolean;
    namedChunks?: boolean;
    externalDependencies?: string[];
    statsJson?: boolean;
    watch?: boolean;
    poll?: number;
}
interface NgPackagrTarget {
    project: string;
    tsConfig?: string;
    watch?: boolean;
    poll?: number;
}
export {};
