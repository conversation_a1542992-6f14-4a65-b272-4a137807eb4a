{"version": 3, "sources": ["align.js"], "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "defineProperty", "exports", "value", "primitives_js_1", "require", "util_js_1", "zeroWidth", "start", "tag", "type", "name", "getWidth", "markers", "Markers", "w", "tokens", "delimiter", "Math", "max", "space", "len", "padStart", "align", "intoTags", "update", "line", "assign", "isEmpty", "description", "end", "delim", "postDelimiter", "nothingAfter", "postName", "postType", "postTag", "_a", "source", "fields", "reduce", "rewireSource", "map", "default"], "mappings": "AAAA;;AACA,IAAIA,MAAM,GAAI,QAAQ,KAAKA,MAAd,IAAyB,UAAUC,CAAV,EAAaC,CAAb,EAAgB;AAClD,MAAIC,CAAC,GAAG,EAAR;;AACA,OAAK,IAAIC,CAAT,IAAcH,CAAd,EAAiB,IAAII,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCP,CAArC,EAAwCG,CAAxC,KAA8CF,CAAC,CAACO,OAAF,CAAUL,CAAV,IAAe,CAAjE,EACbD,CAAC,CAACC,CAAD,CAAD,GAAOH,CAAC,CAACG,CAAD,CAAR;;AACJ,MAAIH,CAAC,IAAI,IAAL,IAAa,OAAOI,MAAM,CAACK,qBAAd,KAAwC,UAAzD,EACI,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWP,CAAC,GAAGC,MAAM,CAACK,qBAAP,CAA6BT,CAA7B,CAApB,EAAqDU,CAAC,GAAGP,CAAC,CAACQ,MAA3D,EAAmED,CAAC,EAApE,EAAwE;AACpE,QAAIT,CAAC,CAACO,OAAF,CAAUL,CAAC,CAACO,CAAD,CAAX,IAAkB,CAAlB,IAAuBN,MAAM,CAACC,SAAP,CAAiBO,oBAAjB,CAAsCL,IAAtC,CAA2CP,CAA3C,EAA8CG,CAAC,CAACO,CAAD,CAA/C,CAA3B,EACIR,CAAC,CAACC,CAAC,CAACO,CAAD,CAAF,CAAD,GAAUV,CAAC,CAACG,CAAC,CAACO,CAAD,CAAF,CAAX;AACP;AACL,SAAOR,CAAP;AACH,CAVD;;AAWAE,MAAM,CAACS,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,EAAAA,KAAK,EAAE;AAAT,CAA7C;;AACA,MAAMC,eAAe,GAAGC,OAAH,qBAArB;;AACA,MAAMC,SAAS,GAAGD,OAAH,eAAf;;AACA,MAAME,SAAS,GAAG;AACdC,EAAAA,KAAK,EAAE,CADO;AAEdC,EAAAA,GAAG,EAAE,CAFS;AAGdC,EAAAA,IAAI,EAAE,CAHQ;AAIdC,EAAAA,IAAI,EAAE;AAJQ,CAAlB;;AAMA,MAAMC,QAAQ,GAAG,CAACC,OAAO,GAAGT,eAAe,CAACU,OAA3B,KAAuC,CAACC,CAAD,EAAI;AAAEC,EAAAA,MAAM,EAAE1B;AAAV,CAAJ,MAAuB;AAC3EkB,EAAAA,KAAK,EAAElB,CAAC,CAAC2B,SAAF,KAAgBJ,OAAO,CAACL,KAAxB,GAAgClB,CAAC,CAACkB,KAAF,CAAQT,MAAxC,GAAiDgB,CAAC,CAACP,KADiB;AAE3EC,EAAAA,GAAG,EAAES,IAAI,CAACC,GAAL,CAASJ,CAAC,CAACN,GAAX,EAAgBnB,CAAC,CAACmB,GAAF,CAAMV,MAAtB,CAFsE;AAG3EW,EAAAA,IAAI,EAAEQ,IAAI,CAACC,GAAL,CAASJ,CAAC,CAACL,IAAX,EAAiBpB,CAAC,CAACoB,IAAF,CAAOX,MAAxB,CAHqE;AAI3EY,EAAAA,IAAI,EAAEO,IAAI,CAACC,GAAL,CAASJ,CAAC,CAACJ,IAAX,EAAiBrB,CAAC,CAACqB,IAAF,CAAOZ,MAAxB;AAJqE,CAAvB,CAAxD;;AAMA,MAAMqB,KAAK,GAAIC,GAAD,IAAS,GAAGC,QAAH,CAAYD,GAAZ,EAAiB,GAAjB,CAAvB;;AACA,SAASE,KAAT,CAAeV,OAAO,GAAGT,eAAe,CAACU,OAAzC,EAAkD;AAC9C,MAAIU,QAAQ,GAAG,KAAf;AACA,MAAIT,CAAJ;;AACA,WAASU,MAAT,CAAgBC,IAAhB,EAAsB;AAClB,UAAMV,MAAM,GAAGxB,MAAM,CAACmC,MAAP,CAAc,EAAd,EAAkBD,IAAI,CAACV,MAAvB,CAAf;AACA,QAAIA,MAAM,CAACP,GAAP,KAAe,EAAnB,EACIe,QAAQ,GAAG,IAAX;AACJ,UAAMI,OAAO,GAAGZ,MAAM,CAACP,GAAP,KAAe,EAAf,IACZO,MAAM,CAACL,IAAP,KAAgB,EADJ,IAEZK,MAAM,CAACN,IAAP,KAAgB,EAFJ,IAGZM,MAAM,CAACa,WAAP,KAAuB,EAH3B,CAJkB,CAQlB;;AACA,QAAIb,MAAM,CAACc,GAAP,KAAejB,OAAO,CAACiB,GAAvB,IAA8BF,OAAlC,EAA2C;AACvCZ,MAAAA,MAAM,CAACR,KAAP,GAAeY,KAAK,CAACL,CAAC,CAACP,KAAF,GAAU,CAAX,CAApB;AACA,aAAOhB,MAAM,CAACmC,MAAP,CAAcnC,MAAM,CAACmC,MAAP,CAAc,EAAd,EAAkBD,IAAlB,CAAd,EAAuC;AAAEV,QAAAA;AAAF,OAAvC,CAAP;AACH;;AACD,YAAQA,MAAM,CAACC,SAAf;AACI,WAAKJ,OAAO,CAACL,KAAb;AACIQ,QAAAA,MAAM,CAACR,KAAP,GAAeY,KAAK,CAACL,CAAC,CAACP,KAAH,CAApB;AACA;;AACJ,WAAKK,OAAO,CAACkB,KAAb;AACIf,QAAAA,MAAM,CAACR,KAAP,GAAeY,KAAK,CAACL,CAAC,CAACP,KAAF,GAAU,CAAX,CAApB;AACA;;AACJ;AACIQ,QAAAA,MAAM,CAACC,SAAP,GAAmB,EAAnB;AACAD,QAAAA,MAAM,CAACR,KAAP,GAAeY,KAAK,CAACL,CAAC,CAACP,KAAF,GAAU,CAAX,CAApB;AAAmC;AAT3C;;AAWA,QAAI,CAACgB,QAAL,EAAe;AACXR,MAAAA,MAAM,CAACgB,aAAP,GAAuBhB,MAAM,CAACa,WAAP,KAAuB,EAAvB,GAA4B,EAA5B,GAAiC,GAAxD;AACA,aAAOrC,MAAM,CAACmC,MAAP,CAAcnC,MAAM,CAACmC,MAAP,CAAc,EAAd,EAAkBD,IAAlB,CAAd,EAAuC;AAAEV,QAAAA;AAAF,OAAvC,CAAP;AACH;;AACD,UAAMiB,YAAY,GAAG;AACjBF,MAAAA,KAAK,EAAE,KADU;AAEjBtB,MAAAA,GAAG,EAAE,KAFY;AAGjBC,MAAAA,IAAI,EAAE,KAHW;AAIjBC,MAAAA,IAAI,EAAE;AAJW,KAArB;;AAMA,QAAIK,MAAM,CAACa,WAAP,KAAuB,EAA3B,EAA+B;AAC3BI,MAAAA,YAAY,CAACtB,IAAb,GAAoB,IAApB;AACAK,MAAAA,MAAM,CAACkB,QAAP,GAAkB,EAAlB;;AACA,UAAIlB,MAAM,CAACL,IAAP,KAAgB,EAApB,EAAwB;AACpBsB,QAAAA,YAAY,CAACvB,IAAb,GAAoB,IAApB;AACAM,QAAAA,MAAM,CAACmB,QAAP,GAAkB,EAAlB;;AACA,YAAInB,MAAM,CAACN,IAAP,KAAgB,EAApB,EAAwB;AACpBuB,UAAAA,YAAY,CAACxB,GAAb,GAAmB,IAAnB;AACAO,UAAAA,MAAM,CAACoB,OAAP,GAAiB,EAAjB;;AACA,cAAIpB,MAAM,CAACP,GAAP,KAAe,EAAnB,EAAuB;AACnBwB,YAAAA,YAAY,CAACF,KAAb,GAAqB,IAArB;AACH;AACJ;AACJ;AACJ;;AACDf,IAAAA,MAAM,CAACgB,aAAP,GAAuBC,YAAY,CAACF,KAAb,GAAqB,EAArB,GAA0B,GAAjD;AACA,QAAI,CAACE,YAAY,CAACxB,GAAlB,EACIO,MAAM,CAACoB,OAAP,GAAiBhB,KAAK,CAACL,CAAC,CAACN,GAAF,GAAQO,MAAM,CAACP,GAAP,CAAWV,MAAnB,GAA4B,CAA7B,CAAtB;AACJ,QAAI,CAACkC,YAAY,CAACvB,IAAlB,EACIM,MAAM,CAACmB,QAAP,GAAkBf,KAAK,CAACL,CAAC,CAACL,IAAF,GAASM,MAAM,CAACN,IAAP,CAAYX,MAArB,GAA8B,CAA/B,CAAvB;AACJ,QAAI,CAACkC,YAAY,CAACtB,IAAlB,EACIK,MAAM,CAACkB,QAAP,GAAkBd,KAAK,CAACL,CAAC,CAACJ,IAAF,GAASK,MAAM,CAACL,IAAP,CAAYZ,MAArB,GAA8B,CAA/B,CAAvB;AACJ,WAAOP,MAAM,CAACmC,MAAP,CAAcnC,MAAM,CAACmC,MAAP,CAAc,EAAd,EAAkBD,IAAlB,CAAd,EAAuC;AAAEV,MAAAA;AAAF,KAAvC,CAAP;AACH;;AACD,SAAQqB,EAAD,IAAQ;AACX,QAAI;AAAEC,MAAAA;AAAF,QAAaD,EAAjB;AAAA,QAAqBE,MAAM,GAAGpD,MAAM,CAACkD,EAAD,EAAK,CAAC,QAAD,CAAL,CAApC;;AACAtB,IAAAA,CAAC,GAAGuB,MAAM,CAACE,MAAP,CAAc5B,QAAQ,CAACC,OAAD,CAAtB,EAAiCrB,MAAM,CAACmC,MAAP,CAAc,EAAd,EAAkBpB,SAAlB,CAAjC,CAAJ;AACA,WAAO,CAAC,GAAGD,SAAS,CAACmC,YAAd,EAA4BjD,MAAM,CAACmC,MAAP,CAAcnC,MAAM,CAACmC,MAAP,CAAc,EAAd,EAAkBY,MAAlB,CAAd,EAAyC;AAAED,MAAAA,MAAM,EAAEA,MAAM,CAACI,GAAP,CAAWjB,MAAX;AAAV,KAAzC,CAA5B,CAAP;AACH,GAJD;AAKH;;AACDvB,OAAO,CAACyC,OAAR,GAAkBpB,KAAlB", "sourcesContent": ["\"use strict\";\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst primitives_js_1 = require(\"../primitives.js\");\nconst util_js_1 = require(\"../util.js\");\nconst zeroWidth = {\n    start: 0,\n    tag: 0,\n    type: 0,\n    name: 0,\n};\nconst getWidth = (markers = primitives_js_1.Markers) => (w, { tokens: t }) => ({\n    start: t.delimiter === markers.start ? t.start.length : w.start,\n    tag: Math.max(w.tag, t.tag.length),\n    type: Math.max(w.type, t.type.length),\n    name: Math.max(w.name, t.name.length),\n});\nconst space = (len) => ''.padStart(len, ' ');\nfunction align(markers = primitives_js_1.Markers) {\n    let intoTags = false;\n    let w;\n    function update(line) {\n        const tokens = Object.assign({}, line.tokens);\n        if (tokens.tag !== '')\n            intoTags = true;\n        const isEmpty = tokens.tag === '' &&\n            tokens.name === '' &&\n            tokens.type === '' &&\n            tokens.description === '';\n        // dangling '*/'\n        if (tokens.end === markers.end && isEmpty) {\n            tokens.start = space(w.start + 1);\n            return Object.assign(Object.assign({}, line), { tokens });\n        }\n        switch (tokens.delimiter) {\n            case markers.start:\n                tokens.start = space(w.start);\n                break;\n            case markers.delim:\n                tokens.start = space(w.start + 1);\n                break;\n            default:\n                tokens.delimiter = '';\n                tokens.start = space(w.start + 2); // compensate delimiter\n        }\n        if (!intoTags) {\n            tokens.postDelimiter = tokens.description === '' ? '' : ' ';\n            return Object.assign(Object.assign({}, line), { tokens });\n        }\n        const nothingAfter = {\n            delim: false,\n            tag: false,\n            type: false,\n            name: false,\n        };\n        if (tokens.description === '') {\n            nothingAfter.name = true;\n            tokens.postName = '';\n            if (tokens.name === '') {\n                nothingAfter.type = true;\n                tokens.postType = '';\n                if (tokens.type === '') {\n                    nothingAfter.tag = true;\n                    tokens.postTag = '';\n                    if (tokens.tag === '') {\n                        nothingAfter.delim = true;\n                    }\n                }\n            }\n        }\n        tokens.postDelimiter = nothingAfter.delim ? '' : ' ';\n        if (!nothingAfter.tag)\n            tokens.postTag = space(w.tag - tokens.tag.length + 1);\n        if (!nothingAfter.type)\n            tokens.postType = space(w.type - tokens.type.length + 1);\n        if (!nothingAfter.name)\n            tokens.postName = space(w.name - tokens.name.length + 1);\n        return Object.assign(Object.assign({}, line), { tokens });\n    }\n    return (_a) => {\n        var { source } = _a, fields = __rest(_a, [\"source\"]);\n        w = source.reduce(getWidth(markers), Object.assign({}, zeroWidth));\n        return (0, util_js_1.rewireSource)(Object.assign(Object.assign({}, fields), { source: source.map(update) }));\n    };\n}\nexports.default = align;\n"], "file": "align.cjs"}