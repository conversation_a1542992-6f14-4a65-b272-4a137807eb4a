export declare class DummyEleventyConfig {
    _getUniqueId(): void;
    reset(): void;
    versionCheck(): void;
    on(): void;
    emit(): void;
    _enablePluginExecution(): void;
    addMarkdownHighlighter(): void;
    addLiquidTag(): void;
    addLiquidFilter(): void;
    addNunjucksAsyncFilter(): void;
    addNunjucksFilter(): void;
    addHandlebarsHelper(): void;
    addFilter(): void;
    addAsyncFilter(): void;
    getFilter(): void;
    addNunjucksTag(): void;
    addGlobalData(): void;
    addNunjucksGlobal(): void;
    addTransform(): void;
    addLinter(): void;
    addLayoutAlias(): void;
    setLayoutResolution(): void;
    enableLayoutResolution(): void;
    getCollections(): void;
    addCollection(): void;
    addPlugin(): void;
    _getPluginName(): void;
    _executePlugin(): void;
    getNamespacedName(): void;
    namespace(): void;
    addPassthroughCopy(input: string | Record<string, string>): void;
    _normalizeTemplateFormats(): void;
    setTemplateFormats(): void;
    addTemplateFormats(): void;
    setLibrary(): void;
    amendLibrary(): void;
    setPugOptions(): void;
    setLiquidOptions(): void;
    setNunjucksEnvironmentOptions(): void;
    setNunjucksPrecompiledTemplates(): void;
    setEjsOptions(): void;
    setDynamicPermalinks(): void;
    setUseGitIgnore(): void;
    addShortcode(): void;
    addAsyncShortcode(): void;
    addNunjucksAsyncShortcode(): void;
    addNunjucksShortcode(): void;
    addLiquidShortcode(): void;
    addHandlebarsShortcode(): void;
    addPairedShortcode(): void;
    addPairedAsyncShortcode(): void;
    addPairedNunjucksAsyncShortcode(): void;
    addPairedNunjucksShortcode(): void;
    addPairedLiquidShortcode(): void;
    addPairedHandlebarsShortcode(): void;
    addJavaScriptFunction(): void;
    setDataDeepMerge(): void;
    isDataDeepMergeModified(): void;
    addWatchTarget(): void;
    setWatchJavaScriptDependencies(): void;
    setServerOptions(): void;
    setBrowserSyncConfig(): void;
    setChokidarConfig(): void;
    setWatchThrottleWaitTime(): void;
    setFrontMatterParsingOptions(): void;
    setQuietMode(): void;
    addExtension(): void;
    addDataExtension(): void;
    setUseTemplateCache(): void;
    setPrecompiledCollections(): void;
    setServerPassthroughCopyBehavior(): void;
    addUrlTransform(): void;
    setDataFileSuffixes(): void;
    setDataFileBaseName(): void;
    getMergingConfigObject(): void;
    isVirtualTemplate(): void;
    setInputDirectory(): void;
    setOutputDirectory(): void;
    setDataDirectory(): void;
    setIncludesDirectory(): void;
    setLayoutsDirectory(): void;
    setFreezeReservedData(): void;
    addDateParsing(): void;
    _uniqueId: {};
    events: {};
    benchmarkManager: {};
    benchmarks: {};
    collections: {};
    precompiledCollections: {};
    templateFormats: {};
    liquidOptions: {};
    liquidTags: {};
    liquidFilters: {};
    liquidShortcodes: {};
    liquidPairedShortcodes: {};
    nunjucksEnvironmentOptions: {};
    nunjucksPrecompiledTemplates: {};
    nunjucksFilters: {};
    nunjucksAsyncFilters: {};
    nunjucksTags: {};
    nunjucksGlobals: {};
    nunjucksShortcodes: {};
    nunjucksAsyncShortcodes: {};
    nunjucksPairedShortcodes: {};
    nunjucksAsyncPairedShortcodes: {};
    javascriptFunctions: {};
    markdownHighlighter: null;
    libraryOverrides: {};
    passthroughCopies: Record<string, Record<never, never>>;
    layoutAliases: {};
    layoutResolution: boolean;
    linters: {};
    transforms: {};
    activeNamespace: string;
    DateTime: {};
    dynamicPermalinks: boolean;
    useGitIgnore: boolean;
    ignores: Set<unknown>;
    watchIgnores: Set<unknown>;
    dataDeepMerge: boolean;
    extensionMap: Set<unknown>;
    watchJavaScriptDependencies: boolean;
    additionalWatchTargets: never[];
    serverOptions: {};
    globalData: {};
    chokidarConfig: {};
    watchThrottleWaitTime: number;
    dataExtensions: Map<any, any>;
    quietMode: boolean;
    plugins: never[];
    _pluginExecution: boolean;
    useTemplateCache: boolean;
    dataFilterSelectors: Set<unknown>;
    libraryAmendments: {};
    serverPassthroughCopyBehavior: string;
    urlTransforms: never[];
    dataFileSuffixesOverride: boolean;
    dataFileDirBaseNameOverride: boolean;
    frontMatterParsingOptions: {
        engines: {};
    };
    templateFormatsAdded: {};
}
export declare const defaultEleventyConfig: {
    dir: {
        input: string;
        output: string;
        includes: string;
        layouts: string;
        data: string;
    };
    templateFormats: string;
};
