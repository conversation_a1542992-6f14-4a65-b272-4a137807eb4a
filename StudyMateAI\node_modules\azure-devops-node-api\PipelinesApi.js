"use strict";
/*
 * ---------------------------------------------------------
 * Copyright(C) Microsoft Corporation. All rights reserved.
 * ---------------------------------------------------------
 *
 * ---------------------------------------------------------
 * Generated file, DO NOT EDIT
 * ---------------------------------------------------------
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PipelinesApi = void 0;
const basem = require("./ClientApiBases");
const PipelinesInterfaces = require("./interfaces/PipelinesInterfaces");
class PipelinesApi extends basem.ClientApiBase {
    constructor(baseUrl, handlers, options) {
        super(baseUrl, handlers, 'node-Pipelines-api', options);
    }
    /**
     * Get a specific artifact from a pipeline run
     *
     * @param {string} project - Project ID or project name
     * @param {number} pipelineId - ID of the pipeline.
     * @param {number} runId - ID of the run of that pipeline.
     * @param {string} artifactName - Name of the artifact.
     * @param {PipelinesInterfaces.GetArtifactExpandOptions} expand - Expand options. Default is None.
     */
    getArtifact(project, pipelineId, runId, artifactName, expand) {
        return __awaiter(this, void 0, void 0, function* () {
            if (artifactName == null) {
                throw new TypeError('artifactName can not be null or undefined');
            }
            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
                let routeValues = {
                    project: project,
                    pipelineId: pipelineId,
                    runId: runId
                };
                let queryValues = {
                    artifactName: artifactName,
                    '$expand': expand,
                };
                try {
                    let verData = yield this.vsoClient.getVersioningData("7.2-preview.1", "pipelines", "85023071-bd5e-4438-89b0-2a5bf362a19d", routeValues, queryValues);
                    let url = verData.requestUrl;
                    let options = this.createRequestOptions('application/json', verData.apiVersion);
                    let res;
                    res = yield this.rest.get(url, options);
                    let ret = this.formatResponse(res.result, PipelinesInterfaces.TypeInfo.Artifact, false);
                    resolve(ret);
                }
                catch (err) {
                    reject(err);
                }
            }));
        });
    }
    /**
     * Get a specific log from a pipeline run
     *
     * @param {string} project - Project ID or project name
     * @param {number} pipelineId - ID of the pipeline.
     * @param {number} runId - ID of the run of that pipeline.
     * @param {number} logId - ID of the log.
     * @param {PipelinesInterfaces.GetLogExpandOptions} expand - Expand options. Default is None.
     */
    getLog(project, pipelineId, runId, logId, expand) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
                let routeValues = {
                    project: project,
                    pipelineId: pipelineId,
                    runId: runId,
                    logId: logId
                };
                let queryValues = {
                    '$expand': expand,
                };
                try {
                    let verData = yield this.vsoClient.getVersioningData("7.2-preview.1", "pipelines", "fb1b6d27-3957-43d5-a14b-a2d70403e545", routeValues, queryValues);
                    let url = verData.requestUrl;
                    let options = this.createRequestOptions('application/json', verData.apiVersion);
                    let res;
                    res = yield this.rest.get(url, options);
                    let ret = this.formatResponse(res.result, PipelinesInterfaces.TypeInfo.Log, false);
                    resolve(ret);
                }
                catch (err) {
                    reject(err);
                }
            }));
        });
    }
    /**
     * Get a list of logs from a pipeline run.
     *
     * @param {string} project - Project ID or project name
     * @param {number} pipelineId - ID of the pipeline.
     * @param {number} runId - ID of the run of that pipeline.
     * @param {PipelinesInterfaces.GetLogExpandOptions} expand - Expand options. Default is None.
     */
    listLogs(project, pipelineId, runId, expand) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
                let routeValues = {
                    project: project,
                    pipelineId: pipelineId,
                    runId: runId
                };
                let queryValues = {
                    '$expand': expand,
                };
                try {
                    let verData = yield this.vsoClient.getVersioningData("7.2-preview.1", "pipelines", "fb1b6d27-3957-43d5-a14b-a2d70403e545", routeValues, queryValues);
                    let url = verData.requestUrl;
                    let options = this.createRequestOptions('application/json', verData.apiVersion);
                    let res;
                    res = yield this.rest.get(url, options);
                    let ret = this.formatResponse(res.result, PipelinesInterfaces.TypeInfo.LogCollection, false);
                    resolve(ret);
                }
                catch (err) {
                    reject(err);
                }
            }));
        });
    }
    /**
     * Create a pipeline.
     *
     * @param {PipelinesInterfaces.CreatePipelineParameters} inputParameters - Input parameters.
     * @param {string} project - Project ID or project name
     */
    createPipeline(inputParameters, project) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
                let routeValues = {
                    project: project
                };
                try {
                    let verData = yield this.vsoClient.getVersioningData("7.2-preview.1", "pipelines", "28e1305e-2afe-47bf-abaf-cbb0e6a91988", routeValues);
                    let url = verData.requestUrl;
                    let options = this.createRequestOptions('application/json', verData.apiVersion);
                    let res;
                    res = yield this.rest.create(url, inputParameters, options);
                    let ret = this.formatResponse(res.result, PipelinesInterfaces.TypeInfo.Pipeline, false);
                    resolve(ret);
                }
                catch (err) {
                    reject(err);
                }
            }));
        });
    }
    /**
     * Gets a pipeline, optionally at the specified version
     *
     * @param {string} project - Project ID or project name
     * @param {number} pipelineId - The pipeline ID
     * @param {number} pipelineVersion - The pipeline version
     */
    getPipeline(project, pipelineId, pipelineVersion) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
                let routeValues = {
                    project: project,
                    pipelineId: pipelineId
                };
                let queryValues = {
                    pipelineVersion: pipelineVersion,
                };
                try {
                    let verData = yield this.vsoClient.getVersioningData("7.2-preview.1", "pipelines", "28e1305e-2afe-47bf-abaf-cbb0e6a91988", routeValues, queryValues);
                    let url = verData.requestUrl;
                    let options = this.createRequestOptions('application/json', verData.apiVersion);
                    let res;
                    res = yield this.rest.get(url, options);
                    let ret = this.formatResponse(res.result, PipelinesInterfaces.TypeInfo.Pipeline, false);
                    resolve(ret);
                }
                catch (err) {
                    reject(err);
                }
            }));
        });
    }
    /**
     * Get a list of pipelines.
     *
     * @param {string} project - Project ID or project name
     * @param {string} orderBy - A sort expression. Defaults to "name asc"
     * @param {number} top - The maximum number of pipelines to return
     * @param {string} continuationToken - A continuation token from a previous request, to retrieve the next page of results
     */
    listPipelines(project, orderBy, top, continuationToken) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
                let routeValues = {
                    project: project
                };
                let queryValues = {
                    orderBy: orderBy,
                    '$top': top,
                    continuationToken: continuationToken,
                };
                try {
                    let verData = yield this.vsoClient.getVersioningData("7.2-preview.1", "pipelines", "28e1305e-2afe-47bf-abaf-cbb0e6a91988", routeValues, queryValues);
                    let url = verData.requestUrl;
                    let options = this.createRequestOptions('application/json', verData.apiVersion);
                    let res;
                    res = yield this.rest.get(url, options);
                    let ret = this.formatResponse(res.result, PipelinesInterfaces.TypeInfo.Pipeline, true);
                    resolve(ret);
                }
                catch (err) {
                    reject(err);
                }
            }));
        });
    }
    /**
     * Queues a dry run of the pipeline and returns an object containing the final yaml.
     *
     * @param {PipelinesInterfaces.RunPipelineParameters} runParameters - Optional additional parameters for this run.
     * @param {string} project - Project ID or project name
     * @param {number} pipelineId - The pipeline ID.
     * @param {number} pipelineVersion - The pipeline version.
     */
    preview(runParameters, project, pipelineId, pipelineVersion) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
                let routeValues = {
                    project: project,
                    pipelineId: pipelineId
                };
                let queryValues = {
                    pipelineVersion: pipelineVersion,
                };
                try {
                    let verData = yield this.vsoClient.getVersioningData("7.2-preview.1", "pipelines", "53df2d18-29ea-46a9-bee0-933540f80abf", routeValues, queryValues);
                    let url = verData.requestUrl;
                    let options = this.createRequestOptions('application/json', verData.apiVersion);
                    let res;
                    res = yield this.rest.create(url, runParameters, options);
                    let ret = this.formatResponse(res.result, null, false);
                    resolve(ret);
                }
                catch (err) {
                    reject(err);
                }
            }));
        });
    }
    /**
     * Gets a run for a particular pipeline.
     *
     * @param {string} project - Project ID or project name
     * @param {number} pipelineId - The pipeline id
     * @param {number} runId - The run id
     */
    getRun(project, pipelineId, runId) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
                let routeValues = {
                    project: project,
                    pipelineId: pipelineId,
                    runId: runId
                };
                try {
                    let verData = yield this.vsoClient.getVersioningData("7.2-preview.1", "pipelines", "7859261e-d2e9-4a68-b820-a5d84cc5bb3d", routeValues);
                    let url = verData.requestUrl;
                    let options = this.createRequestOptions('application/json', verData.apiVersion);
                    let res;
                    res = yield this.rest.get(url, options);
                    let ret = this.formatResponse(res.result, PipelinesInterfaces.TypeInfo.Run, false);
                    resolve(ret);
                }
                catch (err) {
                    reject(err);
                }
            }));
        });
    }
    /**
     * Gets top 10000 runs for a particular pipeline.
     *
     * @param {string} project - Project ID or project name
     * @param {number} pipelineId - The pipeline id
     */
    listRuns(project, pipelineId) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
                let routeValues = {
                    project: project,
                    pipelineId: pipelineId
                };
                try {
                    let verData = yield this.vsoClient.getVersioningData("7.2-preview.1", "pipelines", "7859261e-d2e9-4a68-b820-a5d84cc5bb3d", routeValues);
                    let url = verData.requestUrl;
                    let options = this.createRequestOptions('application/json', verData.apiVersion);
                    let res;
                    res = yield this.rest.get(url, options);
                    let ret = this.formatResponse(res.result, PipelinesInterfaces.TypeInfo.Run, true);
                    resolve(ret);
                }
                catch (err) {
                    reject(err);
                }
            }));
        });
    }
    /**
     * Runs a pipeline.
     *
     * @param {PipelinesInterfaces.RunPipelineParameters} runParameters - Optional additional parameters for this run.
     * @param {string} project - Project ID or project name
     * @param {number} pipelineId - The pipeline ID.
     * @param {number} pipelineVersion - The pipeline version.
     */
    runPipeline(runParameters, project, pipelineId, pipelineVersion) {
        return __awaiter(this, void 0, void 0, function* () {
            return new Promise((resolve, reject) => __awaiter(this, void 0, void 0, function* () {
                let routeValues = {
                    project: project,
                    pipelineId: pipelineId
                };
                let queryValues = {
                    pipelineVersion: pipelineVersion,
                };
                try {
                    let verData = yield this.vsoClient.getVersioningData("7.2-preview.1", "pipelines", "7859261e-d2e9-4a68-b820-a5d84cc5bb3d", routeValues, queryValues);
                    let url = verData.requestUrl;
                    let options = this.createRequestOptions('application/json', verData.apiVersion);
                    let res;
                    res = yield this.rest.create(url, runParameters, options);
                    let ret = this.formatResponse(res.result, PipelinesInterfaces.TypeInfo.Run, false);
                    resolve(ret);
                }
                catch (err) {
                    reject(err);
                }
            }));
        });
    }
}
exports.PipelinesApi = PipelinesApi;
