{"author": "<PERSON> <<EMAIL>>", "name": "chai", "description": "BDD/TDD assertion library for node.js and the browser. Test framework agnostic.", "keywords": ["test", "assertion", "assert", "testing", "chai"], "homepage": "http://chaijs.com", "license": "MIT", "contributors": ["<PERSON> <<EMAIL>>", "Domenic Denicola <<EMAIL>> (http://domenicdenicola.com)", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "version": "4.5.0", "repository": {"type": "git", "url": "https://github.com/chaijs/chai"}, "bugs": {"url": "https://github.com/chaijs/chai/issues"}, "main": "./index", "exports": {".": {"require": "./index.js", "import": "./index.mjs"}, "./*": "./*"}, "scripts": {"test": "make test"}, "engines": {"node": ">=4"}, "dependencies": {"assertion-error": "^1.1.0", "check-error": "^1.0.3", "deep-eql": "^4.1.3", "get-func-name": "^2.0.2", "loupe": "^2.3.6", "pathval": "^1.1.1", "type-detect": "^4.1.0"}, "devDependencies": {"browserify": "^16.5.2", "bump-cli": "^2.7.1", "codecov": "^3.8.3", "istanbul": "^0.4.5", "karma": "^6.4.2", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.3.0", "karma-mocha": "^2.0.1", "karma-sauce-launcher": "^4.1.4", "mocha": "^10.2.0"}}