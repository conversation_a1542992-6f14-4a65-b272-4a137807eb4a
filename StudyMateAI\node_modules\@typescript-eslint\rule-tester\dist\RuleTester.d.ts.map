{"version": 3, "file": "RuleTester.d.ts", "sourceRoot": "", "sources": ["../src/RuleTester.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAAY,OAAO,EAAE,MAAM,0BAA0B,CAAC;AAClE,OAAO,KAAK,EAEV,aAAa,EAGb,UAAU,EACX,MAAM,oCAAoC,CAAC;AAc5C,OAAO,KAAK,EACV,eAAe,EAEf,gBAAgB,EAChB,QAAQ,EAGR,aAAa,EACd,MAAM,SAAS,CAAC;AAEjB,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAoIhD,qBAAa,UAAW,SAAQ,aAAa;;IAK3C;;OAEG;gBACS,YAAY,CAAC,EAAE,gBAAgB;IA4E3C;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,gBAAgB,GAAG,IAAI;IAcvD;;OAEG;IACH,MAAM,CAAC,gBAAgB,IAAI,QAAQ,CAAC,gBAAgB,CAAC;IAIrD;;;OAGG;IACH,MAAM,CAAC,kBAAkB,IAAI,IAAI;IAIjC;;OAEG;IACH,MAAM,CAAC,IAAI,CAAC,OAAO,SAAS,SAAS,OAAO,EAAE,EAC5C,IAAI,EAAE,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,GACpC,aAAa,CAAC,OAAO,CAAC;IACzB;;OAEG;IACH,MAAM,CAAC,IAAI,CAAC,UAAU,SAAS,MAAM,EAAE,OAAO,SAAS,SAAS,OAAO,EAAE,EACvE,IAAI,EAAE,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,GACzC,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC;IAwJvC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI;IAgCnD;;OAEG;IACH,GAAG,CAAC,UAAU,SAAS,MAAM,EAAE,OAAO,SAAS,SAAS,OAAO,EAAE,EAC/D,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,UAAU,CAAC,UAAU,EAAE,OAAO,CAAC,EACrC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GACpE,IAAI;IA6HP;;;;OAIG;IACH,OAAO,CAAC,cAAc;CAuvBvB"}