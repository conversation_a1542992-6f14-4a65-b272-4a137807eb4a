/*!
   Copyright 2019 <PERSON>

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
import { Disposable } from "./disposable.js";
export declare type DisposableLike = Disposable | (() => void);
/**
 * Emulates Python's `ExitStack`
 */
export declare class DisposableStack {
    [Symbol.toStringTag]: string;
    /**
     * Creates a new DisposableStack.
     */
    constructor();
    static get [Symbol.species](): typeof DisposableStack;
    /**
     * Dispose this object's resources.
     *
     * NOTE: `dispose` returns a bound method, so it can be extracted from `DisposableStack` and called independently:
     *
     * ```ts
     * const stack = new DisposableStack();
     * for (const f of files) stack.use(openFile(f));
     * const closeFiles = stack.dispose;
     * ...
     * closeFiles();
     * ```
     */
    get dispose(): () => void;
    /**
     * Pushes a new disposable resource onto the disposable stack stack. Resources are disposed in the reverse order they were entered.
     * @param value The resource to add.
     * @returns The resource provided.
     */
    use<T extends DisposableLike | null | undefined>(value: T): T;
    /**
     * Pushes a new disposable resource onto the disposable stack stack. Resources are disposed in the reverse order they were entered.
     * @param value The resource to add.
     * @param onDispose The operation to perform when the resource is disposed.
     * @returns The resource provided.
     */
    use<T>(value: T, onDispose: (value: T) => void): T;
    /**
     * Moves all resources out of this `DisposableStack` and into a new `DisposableStack` and returns it.
     */
    move(): DisposableStack;
    /**
     * Dispose this object's resources.
     */
    [Disposable.dispose](): void;
}
//# sourceMappingURL=disposableStack.d.ts.map