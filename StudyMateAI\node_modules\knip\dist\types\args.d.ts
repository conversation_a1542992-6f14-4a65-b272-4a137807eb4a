import type { ParsedArgs } from 'minimist';
export type ConfigArg = boolean | (string | [string, (id: string) => string])[];
export type Args = {
    binaries?: string[];
    positional?: boolean;
    string?: string[];
    boolean?: string[];
    alias?: Record<string, string[]>;
    resolve?: string[];
    nodeImportArgs?: boolean;
    config?: ConfigArg;
    args?: (args: string[]) => string[];
    fromArgs?: string[] | ((parsed: ParsedArgs, args: string[]) => string[]);
};
