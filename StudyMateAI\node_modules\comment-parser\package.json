{"name": "comment-parser", "version": "1.4.1", "description": "Generic J<PERSON>oc-like comment parser", "type": "module", "main": "lib/index.cjs", "exports": {".": {"import": "./es6/index.js", "require": "./lib/index.cjs"}, "./primitives": {"import": "./es6/primitives.js", "require": "./lib/primitives.cjs"}, "./util": {"import": "./es6/util.js", "require": "./lib/util.cjs"}, "./parser/*": {"import": "./es6/parser/*.js", "require": "./lib/parser/*.cjs"}, "./stringifier/*": {"import": "./es6/stringifier/*.js", "require": "./lib/stringifier/*.cjs"}, "./transforms/*": {"import": "./es6/transforms/*.js", "require": "./lib/transforms/*.cjs"}}, "types": "lib/index.d.ts", "directories": {"test": "tests"}, "devDependencies": {"@types/jest": "^26.0.23", "convert-extension": "^0.3.0", "jest": "^27.0.5", "prettier": "2.3.1", "rimraf": "^3.0.2", "rollup": "^2.52.2", "ts-jest": "^27.0.3", "typescript": "^4.9.5"}, "engines": {"node": ">= 12.0.0"}, "scripts": {"build": "rimraf lib es6 browser; tsc -p tsconfig.json && tsc -p tsconfig.node.json && rollup -o browser/index.js -f iife --context window -n CommentParser es6/index.js && convert-extension cjs lib/", "format": "prettier --write src tests", "pretest": "rimraf coverage; npm run build", "test": "prettier --check src tests && jest --verbose", "preversion": "npm run build"}, "repository": {"type": "git", "url": "**************:yavorskiy/comment-parser.git"}, "keywords": ["jsdoc", "comments", "parser"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/syavorsky)", "contributors": ["<PERSON> (https://github.com/alexgrozav)", "<PERSON><PERSON> (https://github.com/zxqfox)", "<PERSON> (https://github.com/blutorange)", "<PERSON> (https://github.com/brettz9)", "<PERSON><PERSON> (https://github.com/doberkofler)", "<PERSON><PERSON><PERSON> (https://github.com/zxcabs)", "<PERSON> \"<PERSON><PERSON><PERSON>\" <PERSON> (https://github.com/jhm-ciberman)", "<PERSON><PERSON> (https://github.com/jaydenseric)", "<PERSON> (https://github.com/ljharb)", "tengattack (https://github.com/tengattack)"], "license": "MIT", "bugs": {"url": "https://github.com/s<PERSON><PERSON><PERSON>/comment-parser/issues"}, "homepage": "https://github.com/sya<PERSON><PERSON>/comment-parser"}