declare const deprecationWarningMessages: {
    readonly ESLINT_LEGACY_ECMAFEATURES: "The 'ecmaFeatures' config file property is deprecated and has no effect.";
};
/**
 * Emits a deprecation warning containing a given filepath. A new deprecation warning is emitted
 * for each unique file path, but repeated invocations with the same file path have no effect.
 * No warnings are emitted if the `--no-deprecation` or `--no-warnings` Node runtime flags are active.
 * @param source The name of the configuration source to report the warning for.
 * @param errorCode The warning message to show.
 */
export declare function emitDeprecationWarning(source: string, errorCode: keyof typeof deprecationWarningMessages): void;
export {};
//# sourceMappingURL=deprecation-warnings.d.ts.map