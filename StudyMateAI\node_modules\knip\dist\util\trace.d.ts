import type { ModuleGraph } from '../types/module-graph.js';
declare const isTrace: boolean;
type CreateNode = {
    identifier?: string;
    hasRef?: boolean;
    isEntry?: boolean;
};
type Create = (filePath: string, options?: CreateNode) => TraceNode;
export type TraceNode = {
    filePath: string;
    identifier?: string;
    hasRef: boolean;
    isEntry: boolean;
    children: Set<TraceNode>;
};
export { isTrace };
export declare const printTrace: (node: TraceNode, filePath: string, identifier?: string) => void;
export declare const createNode: Create;
export declare const addNodes: (node: TraceNode, id: string, importedSymbols: ModuleGraph, filePaths?: Set<string>) => void;
export declare const createAndPrintTrace: (filePath: string, options?: CreateNode) => void;
