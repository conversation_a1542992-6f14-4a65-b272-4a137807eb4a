import type { z } from 'zod';
import { knipConfigurationSchema } from './schema/configuration.js';
import type { Configuration, IgnorePatterns, WorkspaceConfiguration } from './types/config.js';
import type { ConfigurationHints } from './types/issues.js';
import type { PackageJson, WorkspacePackage } from './types/package-json.js';
import { type WorkspaceGraph } from './util/create-workspace-graph.js';
import { type CLIArguments } from './util/get-included-issue-types.js';
export declare const isDefaultPattern: (type: "entry" | "project", id: string) => boolean;
type ConfigurationManagerOptions = {
    cwd: string;
    isProduction: boolean;
    isStrict: boolean;
    isIncludeEntryExports: boolean;
    workspace: string | undefined;
};
export type Workspace = {
    name: string;
    pkgName: string;
    dir: string;
    ancestors: string[];
    config: WorkspaceConfiguration;
    manifestPath: string;
    manifestStr: string;
    ignoreMembers: IgnorePatterns;
    srcDir?: string;
    outDir?: string;
};
export declare class ConfigurationChief {
    cwd: string;
    isProduction: boolean;
    isStrict: boolean;
    isIncludeEntryExports: boolean;
    config: Configuration;
    workspace: string | undefined;
    manifestPath?: string;
    manifest?: PackageJson;
    ignoredWorkspacePatterns: string[];
    workspacePackages: Map<string, WorkspacePackage>;
    workspacesByPkgName: Map<string, Workspace>;
    workspacesByName: Map<string, Workspace>;
    additionalWorkspaceNames: Set<string>;
    availableWorkspaceNames: string[];
    availableWorkspacePkgNames: Set<string>;
    availableWorkspaceDirs: string[];
    workspaceGraph: WorkspaceGraph;
    includedWorkspaces: Workspace[];
    resolvedConfigFilePath?: string;
    rawConfig?: any;
    parsedConfig?: z.infer<typeof knipConfigurationSchema>;
    constructor({ cwd, isProduction, isStrict, isIncludeEntryExports, workspace }: ConfigurationManagerOptions);
    init(): Promise<void>;
    getConfigurationHints(): ConfigurationHints;
    private loadResolvedConfigurationFile;
    getRules(): import("./types/issues.js").Rules;
    getFilters(): {
        dir: string;
    } | {
        dir?: undefined;
    };
    private normalize;
    private setWorkspaces;
    private getListedWorkspaces;
    private getIgnoredWorkspacePatterns;
    private getConfiguredWorkspaceKeys;
    private getAdditionalWorkspaceNames;
    private getAvailableWorkspaceNames;
    private getIncludedWorkspaces;
    getManifestForWorkspace(name: string): PackageJson | undefined;
    getWorkspaces(): Workspace[];
    private getDescendentWorkspaces;
    getIgnoredWorkspacesFor(name: string): string[];
    getNegatedWorkspacePatterns(name: string): string[];
    private getConfigKeyForWorkspace;
    getWorkspaceConfig(workspaceName: string): any;
    getIgnores(workspaceName: string): {
        ignoreBinaries: any[];
        ignoreDependencies: any[];
        ignoreUnresolved: any[];
    };
    getConfigForWorkspace(workspaceName: string, extensions?: string[]): {
        node?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        angular?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        astro?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        ava?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        babel?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        biome?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        bun?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        c8?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        capacitor?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        changelogen?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        changelogithub?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        changesets?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        commitizen?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        commitlint?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        convex?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "create-typescript-app"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        cspell?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        cucumber?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        cypress?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "dependency-cruiser"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        docusaurus?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        dotenv?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        drizzle?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        eleventy?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        eslint?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        expo?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        gatsby?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "github-action"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "github-actions"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        glob?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "graphql-codegen"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        hardhat?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        husky?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "i18next-parser"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        jest?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        karma?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        ladle?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        lefthook?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "lint-staged"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        linthtml?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "lockfile-lint"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "lost-pixel"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        markdownlint?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        metro?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        mocha?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        moonrepo?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        msw?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "nano-staged"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        nest?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        netlify?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        next?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        nodemon?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "npm-package-json-lint"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        nuxt?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        nx?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        nyc?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        oclif?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        oxlint?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        playwright?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "playwright-ct"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "playwright-test"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        plop?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        postcss?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        preconstruct?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        prettier?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        prisma?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "react-cosmos"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "react-router"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        relay?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "release-it"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        remark?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        remix?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        rollup?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        rsbuild?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        rspack?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "semantic-release"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        sentry?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "simple-git-hooks"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "size-limit"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        sst?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        starlight?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        storybook?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        stryker?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        stylelint?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        svelte?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        svgo?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        syncpack?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        tailwind?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        travis?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "ts-node"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        tsdown?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        tsup?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        tsx?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        typedoc?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        typescript?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        unbuild?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        unocss?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "vercel-og"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        vike?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        vite?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        vitest?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        vue?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        "webdriver-io"?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        webpack?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        wireit?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        wrangler?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        xo?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        yarn?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        yorkie?: (boolean | import("./types/config.js").EnsuredPluginConfiguration) | undefined;
        entry: string[];
        project: string[];
        paths: any;
        ignore: string[];
        isIncludeEntryExports: any;
    };
    getIncludedIssueTypes(cliArgs: CLIArguments): import("./types/issues.js").Report;
    findWorkspaceByFilePath(filePath: string): Workspace | undefined;
    getUnusedIgnoredWorkspaces(): string[];
    getTags(): import("./types/cli.js").Tags;
}
export {};
