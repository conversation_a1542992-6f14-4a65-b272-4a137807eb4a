{"name": "knip", "version": "5.61.2", "description": "Find and fix unused dependencies, exports and files in your TypeScript and JavaScript projects", "homepage": "https://knip.dev", "repository": {"type": "git", "url": "git+https://github.com/webpro-nl/knip.git", "directory": "packages/knip"}, "bugs": "https://github.com/webpro-nl/knip/issues", "license": "ISC", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/webpro"}, {"type": "opencollective", "url": "https://opencollective.com/knip"}, {"type": "polar", "url": "https://polar.sh/webpro-nl"}], "main": "./dist/index.js", "bin": {"knip": "bin/knip.js", "knip-bun": "bin/knip-bun.js"}, "type": "module", "types": "./dist/index.d.ts", "scripts": {"knip": "node ./dist/cli.js --directory ../..", "knip:production": "node ./dist/cli.js --directory ../.. --production --strict", "lint": "biome lint ../..", "format": "biome format --write .", "test": "bun test test/*.test.ts test/**/*.test.ts", "test:watch": "bun test test/*.test.ts test/**/*.test.ts --reporter=junit --reporter-outfile=junit.xml || bun test --watch $(grep -o 'name=\"[^\"]*\".*failures=\"[^0]' junit.xml | grep -v 'name=\"bun test\"' | grep -o 'name=\"[^\"]*\"' | cut -d'\"' -f2)", "test:smoke": "bun test test/*.test.ts test/{plugins,util}/*.test.ts", "test:node": "glob -c \"tsx --test --import ./transform-test.js\" \"test/**/*.test.ts\"", "test:node:smoke": "glob -c \"tsx --test --import ./transform-test.js\" \"test/*.test.ts\" && glob -c \"tsx --test --import ./transform-test.js\" \"test/{plugins,util}/*.test.ts\"", "watch": "npm link && tsc --watch", "prebuild": "npm run generate-plugin-defs && node rmdir.js dist", "build": "tsc", "qa": "bun lint && bun run build && bun knip && bun knip:production && bun run test", "release": "release-it", "create-plugin": "bun ./scripts/create-new-plugin.ts", "postcreate-plugin": "bun run build && biome format --write schema.json schema-jsonc.json src/schema/plugins.ts", "generate-plugin-defs": "node ./scripts/generate-plugin-defs.js && biome check --write src/plugins/index.ts src/types/PluginNames.ts src/schema/plugins.ts"}, "files": ["dist", "vendor", "schema.json", "schema-jsonc.json"], "dependencies": {"@nodelib/fs.walk": "^1.2.3", "fast-glob": "^3.3.3", "formatly": "^0.2.4", "jiti": "^2.4.2", "js-yaml": "^4.1.0", "minimist": "^1.2.8", "oxc-resolver": "^11.1.0", "picocolors": "^1.1.1", "picomatch": "^4.0.1", "smol-toml": "^1.3.4", "strip-json-comments": "5.0.2", "zod": "^3.22.4", "zod-validation-error": "^3.0.3"}, "peerDependencies": {"@types/node": ">=18", "typescript": ">=5.0.4"}, "devDependencies": {"@jest/types": "^29.6.3", "@release-it/bumper": "^7.0.5", "@types/bun": "1.2.15", "@types/js-yaml": "^4.0.9", "@types/minimist": "^1.2.5", "@types/picomatch": "3.0.1", "@types/webpack": "^5.28.5", "@wdio/types": "^9.15.0", "codeclimate-types": "^0.3.1", "glob": "^11.0.2", "release-it": "^19.0.3", "tsx": "^4.19.4", "type-fest": "^4.31.0", "typescript": "^5.5.2"}, "engines": {"node": ">=18.18.0"}, "engineStrict": true, "keywords": ["analysis", "analyze", "class", "cli", "dead code", "dependencies", "detect", "devDependencies", "duplicate", "entropy", "enum", "export", "files", "find", "javascript", "lint", "maintenance", "members", "missing", "monorepo", "namespace", "package", "scan", "types", "typescript", "unreferenced", "unresolved", "unused", "workspace"]}