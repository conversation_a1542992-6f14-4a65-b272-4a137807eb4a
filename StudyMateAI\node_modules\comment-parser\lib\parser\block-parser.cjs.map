{"version": 3, "sources": ["block-parser.js"], "names": ["Object", "defineProperty", "exports", "value", "reTag", "<PERSON><PERSON><PERSON><PERSON>", "fence", "fencer", "<PERSON><PERSON><PERSON><PERSON>", "toggleFence", "source", "isFenced", "parseBlock", "sections", "line", "test", "tokens", "description", "push", "length", "default", "split"], "mappings": "AAAA;;AACAA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,EAAAA,KAAK,EAAE;AAAT,CAA7C;AACA,MAAMC,KAAK,GAAG,OAAd;AACA;AACA;AACA;AACA;;AACA,SAASC,SAAT,CAAmB;AAAEC,EAAAA,KAAK,GAAG;AAAV,IAAqB,EAAxC,EAA4C;AACxC,QAAMC,MAAM,GAAGC,SAAS,CAACF,KAAD,CAAxB;;AACA,QAAMG,WAAW,GAAG,CAACC,MAAD,EAASC,QAAT,KAAsBJ,MAAM,CAACG,MAAD,CAAN,GAAiB,CAACC,QAAlB,GAA6BA,QAAvE;;AACA,SAAO,SAASC,UAAT,CAAoBF,MAApB,EAA4B;AAC/B;AACA,UAAMG,QAAQ,GAAG,CAAC,EAAD,CAAjB;AACA,QAAIF,QAAQ,GAAG,KAAf;;AACA,SAAK,MAAMG,IAAX,IAAmBJ,MAAnB,EAA2B;AACvB,UAAIN,KAAK,CAACW,IAAN,CAAWD,IAAI,CAACE,MAAL,CAAYC,WAAvB,KAAuC,CAACN,QAA5C,EAAsD;AAClDE,QAAAA,QAAQ,CAACK,IAAT,CAAc,CAACJ,IAAD,CAAd;AACH,OAFD,MAGK;AACDD,QAAAA,QAAQ,CAACA,QAAQ,CAACM,MAAT,GAAkB,CAAnB,CAAR,CAA8BD,IAA9B,CAAmCJ,IAAnC;AACH;;AACDH,MAAAA,QAAQ,GAAGF,WAAW,CAACK,IAAI,CAACE,MAAL,CAAYC,WAAb,EAA0BN,QAA1B,CAAtB;AACH;;AACD,WAAOE,QAAP;AACH,GAdD;AAeH;;AACDX,OAAO,CAACkB,OAAR,GAAkBf,SAAlB;;AACA,SAASG,SAAT,CAAmBF,KAAnB,EAA0B;AACtB,MAAI,OAAOA,KAAP,KAAiB,QAArB,EACI,OAAQI,MAAD,IAAYA,MAAM,CAACW,KAAP,CAAaf,KAAb,EAAoBa,MAApB,GAA6B,CAA7B,KAAmC,CAAtD;AACJ,SAAOb,KAAP;AACH", "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst reTag = /^@\\S+/;\n/**\n * Creates configured `<PERSON>rser`\n * @param {Partial<Options>} options\n */\nfunction getParser({ fence = '```', } = {}) {\n    const fencer = getFencer(fence);\n    const toggleFence = (source, isFenced) => fencer(source) ? !isFenced : isFenced;\n    return function parseBlock(source) {\n        // start with description section\n        const sections = [[]];\n        let isFenced = false;\n        for (const line of source) {\n            if (reTag.test(line.tokens.description) && !isFenced) {\n                sections.push([line]);\n            }\n            else {\n                sections[sections.length - 1].push(line);\n            }\n            isFenced = toggleFence(line.tokens.description, isFenced);\n        }\n        return sections;\n    };\n}\nexports.default = getParser;\nfunction getFencer(fence) {\n    if (typeof fence === 'string')\n        return (source) => source.split(fence).length % 2 === 0;\n    return fence;\n}\n"], "file": "block-parser.cjs"}