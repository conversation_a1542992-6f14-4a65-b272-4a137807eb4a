import ts from 'typescript';
import type { GetImportsAndExportsOptions } from '../types/config.js';
import type { IssueSymbol } from '../types/issues.js';
import type { ExportMap, ImportMap, UnresolvedImport } from '../types/module-graph.js';
import type { BoundSourceFile } from './SourceFile.js';
export declare const _getImportsAndExports: (sourceFile: BoundSourceFile, resolveModule: (specifier: string) => ts.ResolvedModuleFull | undefined, typeChecker: ts.TypeChecker, options: GetImportsAndExportsOptions) => {
    imports: {
        internal: ImportMap;
        external: Set<string>;
        resolved: Set<string>;
        specifiers: Set<[string, string]>;
        unresolved: Set<UnresolvedImport>;
    };
    exports: ExportMap;
    duplicates: IssueSymbol[][];
    scripts: Set<string>;
    traceRefs: Set<string>;
};
