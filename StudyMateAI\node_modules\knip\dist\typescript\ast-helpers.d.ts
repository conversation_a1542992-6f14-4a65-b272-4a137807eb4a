import ts from 'typescript';
import type { Fix } from '../types/exports.js';
import { SymbolType } from '../types/issues.js';
export declare function isDefaultImport(node: ts.ImportDeclaration | ts.ImportEqualsDeclaration | ts.ExportDeclaration): boolean;
export declare function isAccessExpression(node: ts.Node): node is ts.AccessExpression;
export declare function isImportCall(node: ts.Node): node is ts.ImportCall;
export declare function isRequireCall(callExpression: ts.Node): callExpression is ts.CallExpression;
export declare function isPropertyAccessCall(node: ts.Node, identifier: string): node is ts.CallExpression;
export declare const getNodeType: (node: ts.Node) => SymbolType;
export declare const isNonPrivatePropertyOrMethodDeclaration: (member: ts.ClassElement) => member is ts.MethodDeclaration | ts.PropertyDeclaration;
export declare const getClassMember: (member: ts.MethodDeclaration | ts.PropertyDeclaration, isFixTypes: boolean) => {
    node: ts.MethodDeclaration | ts.PropertyDeclaration;
    identifier: string;
    pos: number;
    type: SymbolType;
    fix: Fix;
};
export declare const getEnumMember: (member: ts.EnumMember, isFixTypes: boolean) => {
    node: ts.EnumMember;
    identifier: string;
    pos: number;
    type: SymbolType;
    fix: Fix;
};
export declare function stripQuotes(name: string): string;
export declare function findAncestor<T>(node: ts.Node | undefined, callback: (element: ts.Node) => boolean | 'STOP'): T | undefined;
export declare function findDescendants<T>(node: ts.Node | undefined, callback: (element: ts.Node) => boolean | 'STOP'): T[];
export declare const isDeclarationFileExtension: (extension: string) => extension is ".d.ts" | ".d.mts" | ".d.cts";
export declare const getJSDocTags: (node: ts.Node) => Set<string>;
export declare const getLineAndCharacterOfPosition: (node: ts.Node, pos: number) => {
    line: number;
    col: number;
    pos: number;
};
export declare const getAccessMembers: (typeChecker: ts.TypeChecker, node: ts.Identifier) => string[];
export declare const isDestructuring: (node: ts.Node) => boolean;
export declare const getDestructuredIds: (name: ts.ObjectBindingPattern) => string[];
export declare const isConsiderReferencedNS: (node: ts.Identifier) => boolean;
export declare const isObjectEnumerationCallExpressionArgument: (node: ts.Identifier) => boolean;
export declare const isInForIteration: (node: ts.Node) => boolean;
export declare const isTopLevel: (node: ts.Node) => boolean;
export declare const getTypeRef: (node: ts.Identifier) => ts.TypeReferenceNode | undefined;
export declare const isImportSpecifier: (node: ts.Node) => boolean;
export declare const isReferencedInExport: (node: ts.Node) => boolean;
export declare const getExportKeywordNode: (node: ts.Node) => ts.ExportKeyword | undefined;
export declare const getDefaultKeywordNode: (node: ts.Node) => ts.DefaultKeyword | undefined;
export declare const hasRequireCall: (node: ts.Node) => boolean;
export declare const isModuleExportsAccess: (node: ts.PropertyAccessExpression) => boolean;
export declare const getImportMap: (sourceFile: ts.SourceFile) => Map<string, string>;
export declare const getDefaultImportName: (importMap: ReturnType<typeof getImportMap>, specifier: string) => string | undefined;
export declare const getPropertyValues: (node: ts.ObjectLiteralExpression, propertyName: string) => Set<string>;
