export declare const toCosmiconfig: (moduleName: string, options?: {
    rcPrefix?: string;
    rcSuffix?: string;
    configDir?: boolean;
    configFiles?: boolean;
    configFilesAllExtensions?: boolean;
    additionalExtensions?: string[];
}) => string[];
export declare const toLilconfig: (moduleName: string, options?: {
    rcPrefix?: string;
    rcSuffix?: string;
    configDir?: boolean;
    configFiles?: boolean;
    configFilesAllExtensions?: boolean;
    additionalExtensions?: string[];
}) => string[];
export declare const toUnconfig: (moduleName: string, options?: {
    rcPrefix?: string;
    rcSuffix?: string;
    configDir?: boolean;
    configFiles?: boolean;
    configFilesAllExtensions?: boolean;
    additionalExtensions?: string[];
}) => string[];
export declare const toC12config: (moduleName: string, options?: {
    rcPrefix?: string;
    rcSuffix?: string;
    configDir?: boolean;
    configFiles?: boolean;
    configFilesAllExtensions?: boolean;
    additionalExtensions?: string[];
}) => string[];
