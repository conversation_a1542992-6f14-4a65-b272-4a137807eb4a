# `@typescript-eslint/rule-tester`

> Tooling to test ESLint rules

[![NPM Version](https://img.shields.io/npm/v/@typescript-eslint/rule-tester.svg?style=flat-square)](https://www.npmjs.com/package/@typescript-eslint/rule-tester)
[![NPM Downloads](https://img.shields.io/npm/dm/@typescript-eslint/rule-tester.svg?style=flat-square)](https://www.npmjs.com/package/@typescript-eslint/rule-tester)

👉 See **https://typescript-eslint.io/packages/rule-tester** for documentation on this package.

<!-- Local path for docs: docs/packages/Rule_Tester.mdx -->
